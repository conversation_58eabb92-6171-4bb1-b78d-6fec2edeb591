创建一个JSON数据的树形可视化显示系统：

1. 视觉设计：
- 使用中文图标：象(对象-蓝色)、组(数组-橙色)、文(字符串-绿色)、数(数字-紫色)、布(布尔-青色)、空(null-深紫)
- 每层级向右递增32px缩进，支持无限层级深度
- 16x16px彩色图标，白色中文字符
- 字体：Microsoft YaHei，行高20px

2. 树形连接线：
- 垂直线从父节点延伸到所有子节点
- 水平线(14px)连接到当前节点
- 最后子节点的垂直线只到中点
- 连接线颜色#d0d0d0
- 层级定位：(层级 × 32px) - 20px

3. 交互功能：
- 折叠按钮：⊞(展开)/⊟(折叠)
- 折叠时显示项目数量("5 keys", "10 items")
- 支持展开全部、折叠全部、按层级折叠
- 空对象/数组不显示折叠按钮

4. 技术实现：
- 递归生成HTML结构
- CSS类名：level-1到level-15，超过15层用deep-level+CSS变量
- 动态计算：padding-left: calc(var(--level) * 32px)
- 唯一ID管理折叠状态

5. 数据操作：
- 复制格式化/压缩JSON
- 粘贴替换功能(带格式验证)
- 导出.json文件
- 实时更新显示