<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON数据解析器</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🔍 JSON数据解析器</h1>
            <p>支持base64解码、gzip解压、JSON格式化等功能</p>
        </header>

        <main>
            <!-- 输入区域 -->
            <section class="input-section">
                <h2>📝 输入JSON数据</h2>
                <div class="input-controls">
                    <button id="loadSampleBtn" class="btn btn-secondary">加载示例数据</button>
                    <button id="clearBtn" class="btn btn-secondary">清空</button>
                    <button id="formatBtn" class="btn btn-primary">格式化JSON</button>
                </div>
                <textarea id="jsonInput" placeholder="请粘贴您的JSON数据..."></textarea>
            </section>

            <!-- 解析结果区域 -->
            <section class="results-section">
                <h2>📊 解析结果</h2>
                
                <!-- 基本信息 -->
                <div class="info-panel">
                    <h3>基本信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>状态码:</label>
                            <span id="statusCode">-</span>
                        </div>
                        <div class="info-item">
                            <label>消息:</label>
                            <span id="message">-</span>
                        </div>
                        <div class="info-item">
                            <label>时间戳:</label>
                            <span id="timestamp">-</span>
                        </div>
                        <div class="info-item">
                            <label>数据类型:</label>
                            <span id="dataType">-</span>
                        </div>
                    </div>
                </div>

                <!-- 数据解析 -->
                <div class="data-panel">
                    <h3>数据内容</h3>
                    <div class="tabs">
                        <button class="tab-btn active" data-tab="raw">原始数据</button>
                        <button class="tab-btn" data-tab="decoded">Base64解码</button>
                        <button class="tab-btn" data-tab="decompressed">Gzip解压</button>
                        <button class="tab-btn" data-tab="formatted">格式化显示</button>
                    </div>
                    
                    <div class="tab-content">
                        <div id="rawTab" class="tab-panel active">
                            <div class="data-info">
                                <span>数据长度: <span id="rawLength">0</span> 字符</span>
                                <button id="copyRawBtn" class="btn btn-small">复制</button>
                            </div>
                            <pre id="rawData"></pre>
                        </div>
                        
                        <div id="decodedTab" class="tab-panel">
                            <div class="data-info">
                                <span>解码后长度: <span id="decodedLength">0</span> 字节</span>
                                <button id="copyDecodedBtn" class="btn btn-small">复制</button>
                                <button id="downloadDecodedBtn" class="btn btn-small">下载</button>
                            </div>
                            <div id="decodedData" class="binary-data">
                                <p>二进制数据，无法直接显示</p>
                            </div>
                        </div>
                        
                        <div id="decompressedTab" class="tab-panel">
                            <div class="data-info">
                                <span>解压后长度: <span id="decompressedLength">0</span> 字符</span>
                                <button id="copyDecompressedBtn" class="btn btn-small">复制</button>
                                <button id="downloadDecompressedBtn" class="btn btn-small">下载</button>
                            </div>
                            <pre id="decompressedData"></pre>
                        </div>
                        
                        <div id="formattedTab" class="tab-panel">
                            <div class="data-info">
                                <span>格式化显示</span>
                                <button id="copyFormattedBtn" class="btn btn-small">复制</button>
                            </div>
                            <div id="formattedData" class="formatted-content"></div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="stats-panel">
                    <h3>统计信息</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value" id="compressionRatio">-</div>
                            <div class="stat-label">压缩比</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="originalSize">-</div>
                            <div class="stat-label">原始大小</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="compressedSize">-</div>
                            <div class="stat-label">压缩大小</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="processingTime">-</div>
                            <div class="stat-label">处理时间</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 工具区域 -->
            <section class="tools-section">
                <h2>🛠️ 实用工具</h2>
                <div class="tools-grid">
                    <div class="tool-item">
                        <h4>JSON验证器</h4>
                        <p>验证JSON格式是否正确</p>
                        <button id="validateBtn" class="btn btn-outline">验证JSON</button>
                    </div>
                    <div class="tool-item">
                        <h4>时间戳转换</h4>
                        <p>转换时间戳为可读格式</p>
                        <button id="convertTimeBtn" class="btn btn-outline">转换时间</button>
                    </div>
                    <div class="tool-item">
                        <h4>数据压缩</h4>
                        <p>压缩文本数据</p>
                        <button id="compressBtn" class="btn btn-outline">压缩数据</button>
                    </div>
                    <div class="tool-item">
                        <h4>导出报告</h4>
                        <p>生成解析报告</p>
                        <button id="exportBtn" class="btn btn-outline">导出报告</button>
                    </div>
                </div>
            </section>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar">
            <div class="status-info">
                <span id="statusText">就绪</span>
            </div>
            <div class="status-actions">
                <button id="helpBtn" class="btn btn-link">帮助</button>
                <button id="aboutBtn" class="btn btn-link">关于</button>
            </div>
        </footer>
    </div>

    <!-- 帮助模态框 -->
    <div id="helpModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>使用帮助</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <h4>功能说明</h4>
                <ul>
                    <li><strong>JSON解析</strong>: 自动解析JSON结构并显示基本信息</li>
                    <li><strong>Base64解码</strong>: 解码base64编码的数据</li>
                    <li><strong>Gzip解压</strong>: 解压gzip压缩的数据</li>
                    <li><strong>格式化显示</strong>: 美化显示解析后的内容</li>
                </ul>
                <h4>使用步骤</h4>
                <ol>
                    <li>将JSON数据粘贴到输入框中</li>
                    <li>点击"格式化JSON"按钮进行解析</li>
                    <li>在不同标签页中查看解析结果</li>
                    <li>使用工具进行进一步处理</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
