/* JSON数据解析器样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主要内容 */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 通用面板样式 */
section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

section h2 {
    margin-bottom: 15px;
    color: #333;
    font-size: 1.3rem;
}

/* 输入区域 */
.input-section {
    flex: 0 0 auto;
}

.input-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

#jsonInput {
    width: 100%;
    height: 150px;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    resize: vertical;
    transition: border-color 0.3s;
}

#jsonInput:focus {
    outline: none;
    border-color: #667eea;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-small {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-link {
    background: none;
    color: #667eea;
    text-decoration: underline;
}

/* 结果区域 */
.results-section {
    flex: 1;
}

/* 信息面板 */
.info-panel {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.info-panel h3 {
    margin-bottom: 10px;
    color: #495057;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.info-item label {
    font-weight: 500;
    color: #666;
}

.info-item span {
    font-weight: 600;
    color: #333;
}

/* 数据面板 */
.data-panel {
    margin-bottom: 20px;
}

.data-panel h3 {
    margin-bottom: 15px;
    color: #495057;
}

/* 标签页 */
.tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 15px;
    overflow-x: auto;
}

.tab-btn {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s;
    white-space: nowrap;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.tab-btn:hover {
    color: #667eea;
    background: #f8f9fa;
}

.tab-content {
    min-height: 200px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.data-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;
    color: #666;
}

.data-info .btn {
    margin-left: 5px;
}

pre {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    line-height: 1.4;
    max-height: 400px;
    overflow-y: auto;
}

.binary-data {
    padding: 20px;
    text-align: center;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 6px;
    border: 2px dashed #dee2e6;
}

.formatted-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    max-height: 400px;
    overflow-y: auto;
}

/* 统计面板 */
.stats-panel {
    margin-bottom: 20px;
}

.stats-panel h3 {
    margin-bottom: 15px;
    color: #495057;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 8px;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 工具区域 */
.tools-section h2 {
    margin-bottom: 20px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.tool-item {
    padding: 20px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s;
}

.tool-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.tool-item h4 {
    margin-bottom: 8px;
    color: #333;
}

.tool-item p {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}

/* 状态栏 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    margin-top: 20px;
    color: white;
}

.status-info {
    font-size: 14px;
}

.status-actions {
    display: flex;
    gap: 10px;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
}

.modal-body h4 {
    margin: 15px 0 10px 0;
    color: #333;
}

.modal-body ul, .modal-body ol {
    margin-left: 20px;
    margin-bottom: 15px;
}

.modal-body li {
    margin-bottom: 5px;
    line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .input-controls {
        flex-direction: column;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
    
    .tabs {
        font-size: 12px;
    }
    
    .tab-btn {
        padding: 8px 12px;
    }
}
