# OB赛前数据分析文档

## 项目目标
获取博彩网平台的赛前比赛数据，专注于两个核心API的详细分析和使用方法。

## 🎯 数据获取策略

### 两步数据获取流程
```
第一步: API #3 → 获取赛事目录和比赛ID列表
第二步: API #4 → 通过比赛ID获取具体比赛详情
```

---

# API #3: 赛事目录API

## 基本信息
- **URL**: `https://api.h8ee0m89.com/yewu11/v2/w/structureTournamentMatchesPB?t=1754562220551`
- **请求方法**: POST
- **核心功能**: 获取所有可用赛事的分类目录和比赛ID列表
- **返回格式**: JSON
- **数据用途**: 为API #4提供比赛ID，构建赛事选择菜单

## 请求参数详解
```json
{
  "cuid": "523963395904882753",    // 客户端用户ID (必需，固定值)
  "sort": 1,                      // 排序方式 (1=默认排序)
  "tid": "",                      // 赛事ID筛选 (空=获取所有赛事)
                                  // 已知ID: 320,360,400,440,480,4780,5500,5580等
  "apiType": 1,                   // API类型 (1=标准类型)
  "orpt": 0,                      // 排序选项 (0=默认)
  "euid": "3020101",              // 企业用户ID (权限控制)
  "selectionHour": null           // 时间筛选 (null=不限时间)
}
```

## 返回数据结构
```json
{
  "livedata": [
    {
      "hipo": true,                    // 是否有图标
      "lurl": "logo_url",              // 赛事Logo URL
      "tf": false,                     // 未知标识
      "mbmty": 1,                      // 比赛类型
      "csid": "1",                     // 分类ID
      "mids": "4593176",               // 比赛ID (重要!)
      "tn": "国际友谊赛U19",            // 赛事名称
      "mgt": "1754557200000",          // 比赛时间戳
      "tid": "6057",                   // 赛事分类ID (重要!)
      "tlev": 11                       // 赛事级别
    }
  ]
}
```

## 关键字段说明
| 字段 | 说明 | 重要性 | 用途 |
|------|------|--------|------|
| `tn` | 赛事名称 | ⭐⭐⭐ | 显示和筛选 |
| `tid` | 赛事分类ID | ⭐⭐⭐⭐⭐ | 用于筛选特定赛事类型 |
| `mids` | 比赛ID | ⭐⭐⭐⭐⭐ | 用于API #4获取比赛详情 |
| `mgt` | 比赛时间戳 | ⭐⭐⭐ | 时间筛选和排序 |
| `tlev` | 赛事级别 | ⭐⭐ | 判断赛事重要性 |

---

# API #4: 比赛详情API

## 基本信息
- **URL**: `https://api.h8ee0m89.com/yewu11/v1/w/structureMatchBaseInfoByMidsPB?t=1754561507741`
- **请求方法**: POST
- **核心功能**: 通过比赛ID批量获取具体比赛信息和投注配置
- **返回格式**: JSON
- **数据用途**: 获取比赛详细信息、球队名称、投注配置

## 请求参数详解
```json
{
  "mids": "4593188,4601476,4601666",  // 比赛ID列表 (逗号分隔)
  "cuid": "523963395904882753",       // 客户端用户ID (必需)
  "cos": 0,                           // 成本选项 (0=默认)
  "orpt": 0,                          // 排序选项 (0=默认)
  "euid": "3020101"                   // 企业用户ID (权限控制)
}
```

### mids参数说明
- **格式**: 逗号分隔的字符串
- **来源**: 从API #3的`mids`字段获取
- **支持**: 批量查询，一次可查询多个比赛
- **示例**: `"4593188,4601476,4601666"`

## 返回数据结构 (完整详细版 - 包含所有盘口和赔率)
```json
{
  "data": [                           // 比赛数据数组，包含11场比赛的详细信息
    {
      // === 基本比赛信息 ===
      "mid": "4593188",               // 比赛唯一标识ID
      "tnjc": "印加尔超",             // 赛事简称 (印度加尔各答超级联赛)
      "csna": "足球",                 // 运动类型 (足球)
      "gcs": 0,                       // 比赛状态 (0=赛前, 1=进行中, 2=结束)
      "mgt": "1754559000000",         // 比赛时间戳 (毫秒) = 2025-08-07 09:30:00
      "mhn": "斯里布米FC",            // 主队名称 (Home team name)
      "man": "加尔各答联SC",          // 客队名称 (Away team name)

      // === 投注相关信息 ===
      "betAmount": "7443.71",         // 该比赛的总投注金额 (反映比赛热度)
      "cosTCorner": false,            // 是否支持角球盘口 (false=不支持)
      "cosTPunish": false,            // 是否支持罚牌盘口 (false=不支持)
      "cosOutright": false,           // 是否支持冠军盘口 (false=不支持)
      "cosOvertime": false,           // 是否支持加时赛投注 (false=不支持)
      "cosPenalty": false,            // 是否支持点球盘口 (false=不支持)

      // === 状态和分类信息 ===
      "mstst": "0",                   // 比赛状态字符串 (0=未开始)
      "st": "T2",                     // 球队类型标识 (T1=主队, T2=客队)
      "cds": "G01",                   // 比赛代码或分组标识
      "mprmc": "GTS",                 // 比赛推广代码或分类
      "mststr": "2504",               // 比赛状态字符串ID
      "mststs": 0,                    // 比赛状态数值
      "tc": "1",                      // 球队分类
      "tf": false,                    // 特殊标识
      "th": 0,                        // 主队优势标识
      "tn": "印度加尔各答超级联赛",   // 完整赛事名称
      "vf": "0",                      // 验证标识

      // === 基础盘口配置 (hpsPns) ===
      "hpsPns": [                     // 主要盘口配置数组
        {
          // 全场独赢盘口
          "ctsp": "1754561483913",    // 创建时间戳 (盘口创建时间)
          "hids": 1,                  // 盘口显示ID
          "hpn": "全场独赢",          // 盘口名称 (中文)
          "hpnb": "全场独赢",         // 盘口名称备用
          "hpt": 1,                   // 盘口类型ID (1=独赢)
          "hshow": "Yes",             // 是否显示
          "mid": "4593188",           // 比赛ID
          "hmm": 0,                   // 盘口模式 (0=简单, 1=复杂)
          "hsw": "1",                 // 支持的投注选项
          "hpid": "1",                // 盘口唯一标识
          "hpon": 10                  // 盘口排序权重
        },
        {
          // 全场让球盘口
          "ctsp": "1754561481723",    // 创建时间戳
          "hids": 1,                  // 盘口显示ID
          "hpn": "全场让球",          // 盘口名称
          "hpnb": "全场让球",         // 盘口名称备用
          "hpt": 2,                   // 盘口类型ID (2=让球)
          "hshow": "Yes",             // 是否显示
          "mid": "4593188",           // 比赛ID
          "hmm": 1,                   // 盘口模式 (1=复杂，有让球数值)
          "hsw": "1,2,3,4,5,6",       // 支持的投注选项 (更多选项)
          "hpid": "4",                // 盘口唯一标识
          "hpon": 7,                  // 盘口排序权重
          "mct": "0.5"                // 让球数值 (主队让0.5球)
        },
        {
          // 全场大小球盘口
          "ctsp": "1754561483913",    // 创建时间戳
          "hids": 1,                  // 盘口显示ID
          "hpn": "全场大小",          // 盘口名称
          "hpnb": "全场大小",         // 盘口名称备用
          "hpt": 5,                   // 盘口类型ID (5=大小球)
          "hshow": "Yes",             // 是否显示
          "mid": "4593188",           // 比赛ID
          "hmm": 1,                   // 盘口模式 (1=复杂，有大小球数值)
          "hsw": "1,2,3,4,5,6",       // 支持的投注选项
          "hpid": "2",                // 盘口唯一标识
          "hpon": 6,                  // 盘口排序权重
          "mct": "1.5"                // 大小球数值 (1.5球)
        },
        {
          // 复合盘口: 独赢 & 进球大小
          "ctsp": "1754561483913",    // 创建时间戳
          "hids": 1,                  // 盘口显示ID
          "hpn": "独赢 & 进球大小",   // 盘口名称
          "hpnb": "独赢 & 进球大小",  // 盘口名称备用
          "hpt": 0,                   // 盘口类型ID (0=复合盘口)
          "hshow": "Yes",             // 是否显示
          "mid": "4593188",           // 比赛ID
          "hmm": 1,                   // 盘口模式
          "hsw": "1",                 // 支持的投注选项
          "hpid": "13",               // 盘口唯一标识
          "hpon": 113                 // 盘口排序权重
        }
      ],

      // === 详细盘口和赔率数据 (hpsCompose) ===
      "hpsCompose": [                 // 复合盘口详细配置数组
        {
          "ctsp": "1754561483913",    // 创建时间戳
          "chpid": "13",              // 复合盘口ID
          "hpid": "13",               // 盘口ID (对应hpsPns中的hpid)
          "hl": {                     // 盘口详细信息 (Handicap Line)
            "hid": "143577862533248579", // 盘口实例唯一ID
            "hs": 0,                  // 盘口状态 (0=正常, 1=暂停, 2=关闭)
            "hv": "1.5",              // 盘口数值 (让球数或大小球数)
            "hmt": 0,                 // 盘口类型标识
            "hn": 1,                  // 盘口编号
            "ol": [                   // 投注选项列表 (Options List)
              {
                "oid": "149451314113045117", // 投注选项唯一ID
                "os": 1,              // 选项状态 (1=可投注, 0=暂停, 2=关闭)
                "otd": 344,           // 选项类型详细ID
                "ot": "1AndNo",       // 选项类型 (1AndNo=主胜&否, 1=主队, Over=大球等)
                "ov": 560000,         // 赔率值 (560000 = 5.60倍，需要除以100000)
                "ov2": "0.86",        // 第二赔率值 (可能是让球盘的水位)
                "onb": "",            // 选项名称简写
                "on": "斯里布米FC & 否", // 选项完整名称
                "onbl": "主胜 & 否",   // 选项标签
                "cds": "G01",         // 分类代码
                "ots": ""             // 选项类型字符串
              },
              {
                "oid": "147172790305412413", // 第二个投注选项
                "os": 1,              // 选项状态
                "otd": 15,            // 选项类型详细ID
                "ot": "XAndOver",     // 选项类型 (XAndOver=平局&大球)
                "ov": 1200000,        // 赔率值 (1200000 = 12.00倍)
                "onb": "1.5",         // 选项名称简写 (大小球数值)
                "on": "平局 & 大 1.5", // 选项完整名称
                "onbl": "平局 & 大 ",  // 选项标签
                "cds": "G01",         // 分类代码
                "ots": ""             // 选项类型字符串
              },
              {
                "oid": "140081533091224556", // 第三个投注选项
                "os": 1,              // 选项状态
                "otd": 16,            // 选项类型详细ID
                "ot": "2AndOver",     // 选项类型 (2AndOver=客胜&大球)
                "ov": 830000,         // 赔率值 (830000 = 8.30倍)
                "onb": "1.5",         // 选项名称简写
                "on": "加尔各答联SC & 大 1.5", // 选项完整名称
                "onbl": "客胜 & 大 ",  // 选项标签
                "cds": "G01",         // 分类代码
                "ots": ""             // 选项类型字符串
              }
            ]
          }
        }
      ],

      // === 主要盘口详细数据 (hps) ===
      "hps": [                        // 主要盘口数组 (包含完整赔率信息)
        {
          "hps": [                    // 盘口列表
            {
              // 全场独赢详细赔率
              "ctsp": "1754561483913", // 创建时间戳
              "chpid": "1",           // 盘口ID
              "hpid": "1",            // 盘口标识
              "hl": {                 // 盘口详细信息
                "hid": "144902857426225383", // 盘口实例ID
                "hs": 0,              // 盘口状态
                "hv": "",             // 盘口数值 (独赢无数值)
                "hmt": 0,             // 盘口类型
                "ol": [               // 投注选项
                  {
                    "oid": "145298469912853331", // 选项ID
                    "os": 1,          // 选项状态
                    "otd": 47,        // 选项类型详细ID
                    "ot": "1",        // 选项类型 (1=主胜)
                    "ov": 450000,     // 赔率值 (450000 = 4.50倍)
                    "onb": "主胜",    // 选项名称简写
                    "on": "斯里布米FC", // 选项完整名称 (主队名)
                    "onbl": "",       // 选项标签
                    "cds": "G01",     // 分类代码
                    "ots": "T1"       // 选项类型字符串 (T1=主队)
                  },
                  {
                    "oid": "143252719337159107", // 选项ID
                    "os": 1,          // 选项状态
                    "otd": 48,        // 选项类型详细ID
                    "ot": "X",        // 选项类型 (X=平局)
                    "ov": 340000,     // 赔率值 (340000 = 3.40倍)
                    "onb": "平局",    // 选项名称简写
                    "on": "平局",     // 选项完整名称
                    "onbl": "",       // 选项标签
                    "cds": "G01",     // 分类代码
                    "ots": "TX"       // 选项类型字符串 (TX=平局)
                  },
                  {
                    "oid": "149127111482248423", // 选项ID
                    "os": 1,          // 选项状态
                    "otd": 49,        // 选项类型详细ID
                    "ot": "2",        // 选项类型 (2=客胜)
                    "ov": 320000,     // 赔率值 (320000 = 3.20倍)
                    "onb": "客胜",    // 选项名称简写
                    "on": "加尔各答联SC", // 选项完整名称 (客队名)
                    "onbl": "",       // 选项标签
                    "cds": "G01",     // 分类代码
                    "ots": "T2"       // 选项类型字符串 (T2=客队)
                  }
                ]
              }
            },
            {
              // 全场让球详细赔率
              "ctsp": "1754561481723", // 创建时间戳
              "chpid": "4",           // 盘口ID
              "hpid": "4",            // 盘口标识
              "hl": {                 // 盘口详细信息
                "hid": "142343005218023031", // 盘口实例ID
                "hs": 0,              // 盘口状态
                "hv": "0.5",          // 让球数值 (主队让0.5球)
                "hmt": 0,             // 盘口类型
                "hn": 1,              // 盘口编号
                "ol": [               // 投注选项
                  {
                    "oid": "143016854795782521", // 选项ID
                    "os": 1,          // 选项状态
                    "otd": 3,         // 选项类型详细ID
                    "ot": "1",        // 选项类型 (1=主队让球)
                    "ov": 172000,     // 赔率值 (172000 = 1.72倍)
                    "ov2": "0.72",    // 水位值 (正数表示高水)
                    "onb": "+0.5",    // 选项名称简写 (实际是客队受让)
                    "on": "+0.5",     // 选项完整名称
                    "onbl": "",       // 选项标签
                    "cds": "G01",     // 分类代码
                    "ots": "T1"       // 选项类型字符串
                  },
                  {
                    "oid": "140972952028556672", // 选项ID
                    "os": 1,          // 选项状态
                    "otd": 4,         // 选项类型详细ID
                    "ot": "2",        // 选项类型 (2=客队受让)
                    "ov": 208000,     // 赔率值 (208000 = 2.08倍)
                    "ov2": "-0.72",   // 水位值 (负数表示低水)
                    "onb": "-0.5",    // 选项名称简写 (实际是主队让球)
                    "on": "-0.5",     // 选项完整名称
                    "onbl": "",       // 选项标签
                    "cds": "G01",     // 分类代码
                    "ots": "T2"       // 选项类型字符串
                  }
                ]
              }
            },
            {
              // 全场大小球详细赔率
              "ctsp": "1754561483913", // 创建时间戳
              "chpid": "2",           // 盘口ID
              "hpid": "2",            // 盘口标识
              "hl": {                 // 盘口详细信息
                "hid": "143942517558738906", // 盘口实例ID
                "hs": 0,              // 盘口状态
                "hv": "1.5",          // 大小球数值 (1.5球)
                "hmt": 0,             // 盘口类型
                "hn": 1,              // 盘口编号
                "ol": [               // 投注选项
                  {
                    "oid": "140101971424217304", // 选项ID
                    "os": 1,          // 选项状态
                    "otd": 2,         // 选项类型详细ID
                    "ot": "Over",     // 选项类型 (Over=大球)
                    "ov": 184000,     // 赔率值 (184000 = 1.84倍)
                    "ov2": "0.84",    // 水位值
                    "onb": "1.5",     // 选项名称简写 (大小球数值)
                    "on": "大 1.5",   // 选项完整名称
                    "onbl": "大 ",    // 选项标签
                    "cds": "G01",     // 分类代码
                    "ots": "T1"       // 选项类型字符串
                  },
                  {
                    "oid": "144501035222744301", // 选项ID
                    "os": 1,          // 选项状态
                    "otd": 1,         // 选项类型详细ID
                    "ot": "Under",    // 选项类型 (Under=小球)
                    "ov": 196000,     // 赔率值 (196000 = 1.96倍)
                    "ov2": "-0.84",   // 水位值
                    "onb": "1.5",     // 选项名称简写
                    "on": "小 1.5",   // 选项完整名称
                    "onbl": "小 ",    // 选项标签
                    "cds": "G01",     // 分类代码
                    "ots": "T2"       // 选项类型字符串
                  }
                ]
              }
            }
          ],

          // === 附加盘口数据 (hpsAdd) ===
          "hpsAdd": [                 // 附加盘口配置 (多重赔率选项)
            {
              "ctsp": "1754561481723", // 创建时间戳
              "chpid": "4",           // 盘口ID
              "hpid": "4",            // 盘口标识
              "hlnm": 1,              // 盘口线数量 (多条线)
              "hl": [                 // 多重盘口线
                {
                  "hid": "142446053732711559", // 盘口实例ID
                  "hs": 0,            // 盘口状态
                  "hv": "0/0.5",      // 让球数值 (0球/0.5球)
                  "hmt": 0,           // 盘口类型
                  "hn": 2,            // 盘口编号
                  "ol": [             // 投注选项
                    {
                      "oid": "142446053732711560", // 选项ID
                      "os": 1,        // 选项状态
                      "ot": 1,        // 选项类型
                      "on": "斯里布米FC 0/0.5", // 选项名称
                      "or": "2.15",   // 赔率
                      "oo": 1         // 选项排序
                    },
                    {
                      "oid": "142446053732711561", // 选项ID
                      "os": 1,        // 选项状态
                      "ot": 2,        // 选项类型
                      "on": "加尔各答联SC +0/0.5", // 选项名称
                      "or": "1.70",   // 赔率
                      "oo": 2         // 选项排序
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],

      // === 15分钟盘口 (hps15Minutes) ===
      "hps15Minutes": [               // 15分钟间隔盘口 (特殊时间段投注)
        {
          "ctsp": "1754561486768",    // 创建时间戳
          "chpid": "3204",            // 盘口ID
          "hpid": "32",               // 盘口标识
          "hl": {                     // 盘口详细信息
            "hid": "148329266127450455", // 盘口实例ID
            "hs": 0,                  // 盘口状态
            "hmt": 0,                 // 盘口类型
            "ad3": "60",              // 附加数据3 (可能是时间段)
            "ad5": "10100000",        // 附加数据5 (可能是配置标识)
            "ol": [                   // 投注选项
              {
                "oid": "148329266127450456", // 选项ID
                "os": 1,              // 选项状态
                "ot": 1,              // 选项类型
                "on": "第60分钟进球", // 选项名称
                "or": "15.00",        // 赔率
                "oo": 1               // 选项排序
              }
            ]
          },
          "hSpecial": "4"             // 特殊盘口标识
        }
      ],

      // === 角球盘口 (hpsCorner) ===
      "hpsCorner": [                  // 角球相关盘口 (当cosTCorner=true时有数据)
        {
          "ctsp": "1754561497108",    // 创建时间戳
          "chpid": "114",             // 盘口ID
          "hpid": "114",              // 盘口标识
          "hl": {                     // 盘口详细信息
            "hid": "149180889006340342", // 盘口实例ID
            "hs": 1,                  // 盘口状态
            "hv": "16",               // 角球数值 (16个角球)
            "hmt": 0,                 // 盘口类型
            "hn": 1,                  // 盘口编号
            "ol": [                   // 投注选项
              {
                "oid": "149180889006340343", // 选项ID
                "os": 1,              // 选项状态
                "ot": 1,              // 选项类型 (1=大于)
                "on": "角球大于16个", // 选项名称
                "or": "1.90",         // 赔率
                "oo": 1               // 选项排序
              },
              {
                "oid": "149180889006340344", // 选项ID
                "os": 1,              // 选项状态
                "ot": 2,              // 选项类型 (2=小于)
                "on": "角球小于16个", // 选项名称
                "or": "1.85",         // 赔率
                "oo": 2               // 选项排序
              }
            ]
          }
        }
      ],

      // === 历史数据和统计 ===
      "msc": [                        // 比赛统计数据 (Match Statistics)
        "S5|0:0",                     // 统计类型5: 比分 0:0
        "S11|0:0",                    // 统计类型11: 半场比分 0:0
        "S12|0:0",                    // 统计类型12: 其他统计
        "S13|0:0",                    // 统计类型13: 角球统计
        "S14|0:0"                     // 统计类型14: 黄牌统计
      ],

      // === 球队相关数据 ===
      "frmhn": ["S"],                 // 主队近期表现 (S=胜, D=平, L=负)
      "frmman": ["L"],                // 客队近期表现

      // === 其他配置信息 ===
      "hpsOvertime": [],              // 加时赛盘口配置 (空数组=无加时赛盘口)
      "hpsTBold": [],                 // 加粗显示的盘口列表 (空=无特殊标记)
      "cosPromotion": false,          // 是否支持升降级盘口
      "mat": "",                      // 比赛附加信息
      "mcid": ""                      // 比赛分类ID
    }
  ]
}
```

## 关键字段详解表

### 盘口类型ID (hpt) 对应表
| hpt值 | 盘口类型 | 说明 | 示例 |
|-------|----------|------|------|
| 0 | 复合盘口 | 组合投注 | 独赢&进球大小 |
| 1 | 全场独赢 | 1X2投注 | 主胜/平局/客胜 |
| 2 | 全场让球 | 亚洲让球盘 | -0.5, +1.5 |
| 5 | 全场大小球 | 大小球盘 | 大于/小于2.5球 |
| 17 | 半场独赢 | 半场1X2 | 半场主胜/平/客胜 |
| 18 | 半场大小球 | 半场大小球 | 半场大于/小于1.5 |
| 19 | 半场让球 | 半场让球盘 | 半场-0.5 |

### 投注选项类型 (ot) 对应表
| ot值 | 选项类型 | 说明 | 示例 |
|------|----------|------|------|
| "1" | 主队胜 | 独赢盘主胜 | 斯里布米FC |
| "X" | 平局 | 独赢盘平局 | 平局 |
| "2" | 客队胜 | 独赢盘客胜 | 加尔各答联SC |
| "Over" | 大球 | 大小球盘大球 | 大 1.5 |
| "Under" | 小球 | 大小球盘小球 | 小 1.5 |
| "1AndOver" | 主胜&大球 | 复合盘口 | 主胜 & 大 1.5 |
| "XAndOver" | 平局&大球 | 复合盘口 | 平局 & 大 1.5 |
| "2AndOver" | 客胜&大球 | 复合盘口 | 客胜 & 大 1.5 |
| "1AndNo" | 主胜&否 | 主胜&两队都不进球 | 主胜 & 否 |

### 盘口状态 (hs) 说明
| hs值 | 状态 | 说明 |
|------|------|------|
| 0 | 正常 | 可正常投注 |
| 1 | 暂停 | 暂时停止投注 |
| 2 | 关闭 | 盘口已关闭 |

### 选项状态 (os) 说明
| os值 | 状态 | 说明 |
|------|------|------|
| 0 | 暂停 | 该选项暂停投注 |
| 1 | 可投注 | 该选项可正常投注 |
| 2 | 关闭 | 该选项已关闭 |

### 赔率计算说明

#### 赔率字段 (ov) 计算方法
- **ov字段**: 存储的是放大100000倍的赔率值
- **实际赔率** = ov ÷ 100000
- **示例**:
  - `"ov": 450000` → 实际赔率 = 4.50倍
  - `"ov": 184000` → 实际赔率 = 1.84倍
  - `"ov": 1200000` → 实际赔率 = 12.00倍

#### 水位字段 (ov2) 说明
- **ov2字段**: 显示水位信息 (仅让球和大小球盘口有此字段)
- **正数**: 表示高水 (如 "0.84")
- **负数**: 表示低水 (如 "-0.72")
- **用途**: 帮助判断盘口倾向和投注热度

#### 实际应用示例
```javascript
// 赔率转换函数
function convertOdds(ov) {
    return (ov / 100000).toFixed(2);
}

// 示例数据
const option = {
    "ov": 450000,
    "ov2": "0.72",
    "on": "斯里布米FC"
};

console.log(`${option.on}: ${convertOdds(option.ov)}倍`);
// 输出: 斯里布米FC: 4.50倍
```

## 关键字段详解
| 字段 | 说明 | 重要性 | 示例值 |
|------|------|--------|--------|
| `tnjc` | 赛事简称 | ⭐⭐⭐ | "印加尔超" |
| `csna` | 运动类型 | ⭐⭐ | "足球" |
| `gcs` | 比赛状态 | ⭐⭐⭐⭐ | 0=赛前, 1=进行中, 2=结束 |
| `mgt` | 比赛时间戳 | ⭐⭐⭐ | "1754559000000" |
| `mhn` | 主队名称 | ⭐⭐⭐⭐⭐ | "斯里布米FC" |
| `man` | 客队名称 | ⭐⭐⭐⭐⭐ | "加尔各答联SC" |
| `betAmount` | 投注金额 | ⭐⭐⭐⭐ | "7443.71" |
| `hpsPns` | 盘口配置 | ⭐⭐⭐ | 投注选项列表 |

## 投注盘口类型详解

### 主要盘口类型 (hpsPns数组)

API #4返回的`hpsPns`数组包含所有支持的投注盘口类型：

```json
{
  "hpsPns": [
    {
      "hpn": "全场独赢",        // 盘口名称
      "hpt": 1,               // 盘口类型ID
      "hshow": "Yes"          // 是否显示
    },
    {
      "hpn": "全场让球",        // 让球盘
      "hpt": 2,
      "hshow": "Yes"
    },
    {
      "hpn": "全场大小",        // 大小球盘
      "hpt": 3,
      "hshow": "Yes"
    }
  ]
}
```

### 完整盘口类型对应表

| 盘口名称 | 盘口类型ID (hpt) | 英文名称 | 说明 |
|----------|------------------|----------|------|
| **全场盘口** | | | |
| 全场独赢 | 1 | 1X2 | 主胜/平局/客胜 |
| 全场让球 | 2 | Asian Handicap | 亚洲让球盘 (如-1.5, +0.5) |
| 全场大小 | 3 | Over/Under | 大小球盘 (如2.5, 3.5) |
| **半场盘口** | | | |
| 半场独赢 | 4 | Half Time 1X2 | 半场主胜/平局/客胜 |
| 半场让球 | 5 | Half Time Handicap | 半场亚洲让球盘 |
| 半场大小 | 6 | Half Time O/U | 半场大小球 |
| **特殊盘口** | | | |
| 波胆 | 7-10 | Correct Score | 正确比分 (如1:0, 2:1) |
| 总进球数 | 11 | Total Goals | 比赛总进球数 |
| 单双 | 12 | Odd/Even | 总进球数单双 |
| 首先进球 | 13 | First Goal | 哪队先进球 |
| 最后进球 | 14 | Last Goal | 哪队最后进球 |

### 特殊盘口标识

除了`hpsPns`数组外，还有特殊的布尔值标识：

```json
{
  "cosTCorner": true,           // 是否支持角球盘口
  "cosTPunish": false,          // 是否支持罚牌盘口
  "cosOutright": false,         // 是否支持冠军盘口
  "cosGoalscorer": false,       // 是否支持射手盘口
  "cosHalfGoal": true,          // 是否支持半场进球盘口
  "cosCleanSheet": false        // 是否支持零封盘口
}
```

### 角球盘口详解

当`cosTCorner: true`时，该比赛支持角球相关投注：

| 角球盘口类型 | 说明 |
|-------------|------|
| 角球总数大小 | 全场角球总数大小球 |
| 角球让球 | 角球数让球盘 |
| 半场角球 | 半场角球数投注 |
| 首个角球 | 哪队获得首个角球 |
| 最后角球 | 哪队获得最后一个角球 |

### 罚牌盘口详解

当`cosTPunish: true`时，该比赛支持罚牌相关投注：

| 罚牌盘口类型 | 说明 |
|-------------|------|
| 黄牌总数 | 全场黄牌总数大小球 |
| 红牌总数 | 全场红牌总数 |
| 首张黄牌 | 哪队获得首张黄牌 |
| 罚牌让球 | 罚牌数让球盘 |

### 实际数据示例

基于API #4返回的真实数据，不同比赛支持的盘口类型：

#### 高级别比赛 (如缅甸女足 vs 澳大利亚U23女足)
```json
{
  "cosTCorner": true,      // 支持角球
  "cosTPunish": true,      // 支持罚牌
  "cosOutright": false,    // 不支持冠军盘
  "hpsPns": [
    {"hpn": "全场独赢", "hpt": 1},
    {"hpn": "全场让球", "hpt": 2},
    {"hpn": "全场大小", "hpt": 3},
    {"hpn": "半场独赢", "hpt": 4},
    {"hpn": "半场让球", "hpt": 5},
    {"hpn": "半场大小", "hpt": 6},
    {"hpn": "波胆", "hpt": 7}
  ]
}
```

#### 低级别比赛 (如地区联赛)
```json
{
  "cosTCorner": false,     // 不支持角球
  "cosTPunish": false,     // 不支持罚牌
  "cosOutright": false,    // 不支持冠军盘
  "hpsPns": [
    {"hpn": "全场独赢", "hpt": 1},
    {"hpn": "全场让球", "hpt": 2},
    {"hpn": "全场大小", "hpt": 3}
  ]
}
```

---

# 数据分析结果

## API #3 包含的赛事类型

### 🏆 欧洲赛事 (高价值)
- **欧洲联赛资格赛**: 10个赛事条目
- **欧洲协会联赛资格赛**: 27个赛事条目
- **总计**: 37个欧洲赛事，包含多场高质量比赛

### 🎮 电竞赛事 (数量最多)
- **VS-PANDA独家EAFC24**: 约150个赛事条目
- **覆盖联赛**: 欧冠、英超、德甲、西甲、意甲、法甲、中超、沙特联等
- **特点**: 模拟真实联赛，24小时不间断

### 🌍 其他真实赛事 (约100个)
- **地区联赛**: 各国低级别联赛
- **友谊赛**: 国际友谊赛、青年友谊赛
- **女子足球**: 各级别女子联赛和锦标赛
- **青年联赛**: U19、U21、U23等年龄组比赛

## API #4 实际比赛数据分析

### 真实足球比赛示例 (9场)
1. **斯里布米FC vs 加尔各答联SC** (印度加尔各答超级联赛) - 投注额: 7,443.71
2. **基德SC vs 加尔各答警察** (印度加尔各答超级联赛) - 投注额: 4,930.33
3. **缅甸(女) vs 澳大利亚U23(女)** (东盟女子锦标赛) - 投注额: 752,643.24
4. **运动 vs 自豪** (白俄罗斯地区西部联赛) - 投注额: 54.68
5. **因邦代罗竞技 vs 卡兰杜拉体育** (安哥拉班图联赛) - 投注额: 8,193.24
6. **齐朗FC vs 运输联** (不丹超级联赛资格赛) - 投注额: 7,554.95
7. **北方虎U23(女) vs 芒特德瑞特城流浪者U23(女)** (澳大利亚女子联赛) - 投注额: 1,038.35
8. **达贡港口U21 vs 达贡星联合U21** (缅甸全国联赛U21) - 投注额: 83,330.29
9. **巴黎圣日耳曼U19 vs 博洛尼亚U19** (青年球会友谊赛) - 投注额: 0.00

### 电竞比赛示例 (2场)
1. **巴塞罗那 vs 马德里竞技** (VS-PANDA独家EAFC24 欧洲冠军联赛) - 投注额: 6.13
2. **拜仁慕尼黑 vs 皇家马德里** (VS-PANDA独家EAFC24 欧洲冠军联赛) - 投注额: 0.00

### 投注热度排行
| 排名 | 比赛 | 投注金额 | 赛事类型 |
|------|------|----------|----------|
| 1 | 缅甸(女) vs 澳大利亚U23(女) | 752,643.24 | 国际女子赛事 |
| 2 | 达贡港口U21 vs 达贡星联合U21 | 83,330.29 | 青年联赛 |
| 3 | 因邦代罗竞技 vs 卡兰杜拉体育 | 8,193.24 | 非洲联赛 |
| 4 | 齐朗FC vs 运输联 | 7,554.95 | 亚洲联赛 |
| 5 | 斯里布米FC vs 加尔各答联SC | 7,443.71 | 印度联赛 |

---

# 实用测试方案

## API #3 参数测试

### 1. 基础获取所有赛事
```json
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "",
  "apiType": 1,
  "orpt": 0,
  "euid": "3020101",
  "selectionHour": null
}
```

### 2. 时间筛选测试
```json
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "",
  "apiType": 1,
  "orpt": 0,
  "euid": "3020101",
  "selectionHour": 12    // 筛选12点的比赛
}
```

### 3. 特定赛事筛选 (基于HTML发现的ID)
```json
// 电竞欧冠
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "400",          // VS-PANDA独家EAFC24 欧洲冠军联赛
  "apiType": 1,
  "orpt": 0,
  "euid": "3020101",
  "selectionHour": null
}

// 梦幻对垒 (10场比赛)
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "4780",         // 梦幻对垒
  "apiType": 1,
  "orpt": 0,
  "euid": "3020101",
  "selectionHour": null
}

// 芬兰丁级联赛 (11场比赛)
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "5940",         // 芬兰丁级联赛
  "apiType": 1,
  "orpt": 0,
  "euid": "3020101",
  "selectionHour": null
}
```

## API #4 参数测试

### 1. 单个比赛查询
```json
{
  "mids": "4593188",
  "cuid": "523963395904882753",
  "cos": 0,
  "orpt": 0,
  "euid": "3020101"
}
```

### 2. 批量比赛查询 (推荐)
```json
{
  "mids": "4593188,4601476,4601666,4604583,4604549",
  "cuid": "523963395904882753",
  "cos": 0,
  "orpt": 0,
  "euid": "3020101"
}
```

### 3. 欧洲赛事专项查询
```json
{
  "mids": "从API #3获取的欧洲赛事mids",
  "cuid": "523963395904882753",
  "cos": 0,
  "orpt": 0,
  "euid": "3020101"
}
```

### 4. 特定盘口类型筛选
```json
{
  "mids": "4593188,4601476",
  "cuid": "523963395904882753",
  "cos": 1,                    // 可能影响盘口显示
  "orpt": 1,                   // 可能影响盘口排序
  "euid": "3020101"
}
```

## 盘口数据获取策略

### 获取完整盘口信息的方法

#### 方法1: 通过API #4确认盘口支持
```json
// 第一步：获取比赛的盘口配置
{
  "mids": "4593188",
  "cuid": "523963395904882753",
  "cos": 0,
  "orpt": 0,
  "euid": "3020101"
}

// 返回结果告诉你该比赛支持哪些盘口类型
```

#### 方法2: 筛选支持特定盘口的比赛
```json
// 使用API #3获取赛事列表，然后用API #4批量检查盘口支持
{
  "mids": "多个比赛ID",
  "cuid": "523963395904882753",
  "cos": 0,
  "orpt": 0,
  "euid": "3020101"
}
```

### 盘口数据分析示例

#### 根据投注热度筛选盘口丰富的比赛
```
高投注额比赛 (如752,643.24) → 通常支持更多盘口类型
- 全场独赢 ✅
- 全场让球 ✅
- 全场大小 ✅
- 半场盘口 ✅
- 角球盘口 ✅
- 罚牌盘口 ✅
- 波胆 ✅

低投注额比赛 (如54.68) → 通常只支持基础盘口
- 全场独赢 ✅
- 全场让球 ✅
- 全场大小 ✅
- 其他盘口 ❌
```

### 寻找具体赔率数据的建议

如果需要获取具体的让球数值、大小球数值和赔率，可以尝试：

#### 1. 调整API参数
```json
// 尝试不同的cos和orpt值
{
  "mids": "4593188",
  "cuid": "523963395904882753",
  "cos": 1,        // 可能返回成本/赔率信息
  "orpt": 2,       // 可能返回详细盘口数据
  "euid": "3020101"
}
```

#### 2. 使用API #3的tid筛选
```json
// 使用特定赛事ID可能获取更详细数据
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "6057",   // 特定赛事ID
  "apiType": 2,    // 尝试不同API类型
  "orpt": 1,
  "euid": "3020101",
  "selectionHour": null
}
```

#### 3. 探索其他可能的API端点
- 可能存在专门的赔率API
- 可能需要不同的URL路径
- 可能需要不同的参数组合

---

# 使用指南

## 完整工作流程

### 步骤1: 获取赛事目录
1. 调用API #3获取所有可用赛事
2. 从返回数据中提取感兴趣的赛事
3. 记录对应的`mids`和`tid`值

### 步骤2: 获取比赛详情
1. 将步骤1获取的`mids`组合成逗号分隔字符串
2. 调用API #4获取具体比赛信息
3. 分析投注热度和比赛质量

### 步骤3: 数据筛选和分析
1. 根据`betAmount`筛选热门比赛
2. 根据`gcs`状态筛选赛前比赛
3. 根据赛事名称筛选目标联赛

## 最佳实践

### 🎯 高效数据获取
- **批量查询**: API #4支持一次查询多个比赛，建议批量使用
- **合理分页**: 如果比赛数量多，分批次查询避免超时
- **缓存机制**: 赛事目录相对稳定，可以缓存API #3的结果

### 📊 数据质量评估
- **投注金额**: 作为比赛热度和关注度的指标
- **赛事级别**: `tlev`字段反映赛事重要性
- **比赛状态**: 确保获取的是赛前数据 (`gcs = 0`)

### 🔍 筛选策略
- **欧洲赛事**: 重点关注`tn`包含"欧洲"的赛事
- **主流联赛**: 筛选投注金额较高的比赛
- **时间范围**: 使用`selectionHour`参数按时间筛选
- **盘口丰富度**: 优先选择支持角球、罚牌等特殊盘口的比赛
- **盘口类型**: 根据需要筛选支持特定盘口类型的比赛

### 🎯 盘口数据分析策略
- **基础盘口**: 所有比赛都支持全场独赢、让球、大小球
- **进阶盘口**: 投注额高的比赛通常支持半场盘口、波胆
- **特殊盘口**: 角球和罚牌盘口通常只在高级别比赛中出现
- **盘口数量**: 可作为比赛重要性和数据完整性的指标

---

# 核心价值总结

## API #3 的核心价值
- ✅ **全量赛事获取** - 一次性获取所有可用赛事分类
- ✅ **数据源头** - 为后续查询提供准确的比赛ID
- ✅ **架构理解** - 了解平台的赛事组织和分类体系
- ✅ **筛选基础** - 支持按时间、赛事类型等维度筛选

## API #4 的核心价值
- ✅ **批量查询** - 高效获取多场比赛的详细信息
- ✅ **完整信息** - 包含球队名称、比赛时间、投注配置等
- ✅ **热度分析** - 通过投注金额判断比赛关注度
- ✅ **盘口配置** - 详细的投注盘口类型和支持情况
- ✅ **盘口筛选** - 可根据盘口类型筛选目标比赛
- ✅ **数据完整性** - 盘口丰富度反映比赛数据质量

## 组合使用的优势
- 🚀 **完整覆盖** - 两个API组合可获取平台所有比赛数据
- 🎯 **精确筛选** - 先目录后详情，实现精准数据获取
- 📈 **高效率** - 避免无效查询，直接获取目标数据
- 🔄 **可扩展** - 为后续获取投注赔率等数据奠定基础
- 🔍 **HTML验证** - 通过网页源码验证API数据的准确性
- 📊 **状态区分** - 明确区分赛前和滚球数据的获取方法

## 数据分析的综合价值

### HTML代码分析价值
- ✅ **参数验证** - 发现了具体的tid参数值和对应关系
- ✅ **数据结构理解** - 理解了平台的赛事分类和编号规律
- ✅ **状态识别** - 明确了"未开赛"状态对应194场赛前比赛
- ✅ **ID模式发现** - 发现了不同数字范围对应不同类型赛事
- ✅ **滚球数据识别** - 发现滚球比赛的HTML结构和特征

### 详细字段注释价值 (1.md)
- ✅ **完整数据理解** - 每个字段的具体含义和作用
- ✅ **盘口配置解析** - hpsPns数组的详细结构说明
- ✅ **状态字段说明** - gcs, mstst, st等状态字段的准确含义
- ✅ **投注金额分析** - betAmount字段反映比赛热度的价值
- ✅ **盘口参数解读** - mct, hsw, hpon等技术参数的实际意义

### 滚球vs赛前对比价值
- ✅ **数据类型区分** - 明确两种数据的获取方法和特征
- ✅ **API参数推测** - 基于HTML分析推测滚球API参数
- ✅ **实时数据理解** - 滚球数据的动态特征和更新频率
- ✅ **比赛状态映射** - HTML显示与API字段的对应关系
- ✅ **盘口差异分析** - 赛前全场盘口 vs 滚球半场盘口

## 🆕 HTML代码分析结果

### 网页结构分析 (基于OB赛事HTML代码.md)

#### 发现的关键信息

##### 1. **比赛状态分类**
从HTML代码中发现了明确的比赛状态分类：
```html
<div class="col-left">未开赛 <span class="match-number">194</span></div>
```
- **未开赛**: 194场比赛 (赛前数据)
- 这与API数据中的 `gcs = 0` 状态对应

##### 2. **赛事ID模式发现**
HTML中的隐藏调试信息显示了数字ID模式：
- `320` - 印度卡纳塔克女子联赛
- `360` - 澳门甲级联赛附加赛
- `400` - VS-PANDA独家EAFC24 欧洲冠军联赛
- `440` - VS-PANDA独家EAFC24 英格兰超级联赛
- `480` - VS-PANDA独家EAFC24 德国甲级联赛
- `4780` - 梦幻对垒 (10场比赛)
- `5500` - 阿根廷联赛后备队 (4场比赛)
- `5580` - 挪威联赛U19 (5场比赛)
- `5620` - 爱沙尼亚联赛U19
- `5820` - 洪都拉斯国家联赛后备队
- `5860` - 印度杜兰德杯
- `5940` - 芬兰丁级联赛 (11场比赛)
- `6020` - 巴西女子杯 (2场比赛)
- `6140` - VS-PANDA独家EAFC24 英格兰超级联赛 (8场比赛)

##### 3. **赛事分类模式**
- **低数字ID (300-600)**: 主要是电竞和特殊赛事
- **中数字ID (4000-6000)**: 真实足球联赛
- **高数字ID (6000+)**: 混合类型，包含电竞和真实赛事

##### 4. **比赛数量统计**
每个赛事显示的比赛数量：
- 单场比赛: 大多数赛事
- 多场比赛: 梦幻对垒(10场)、芬兰丁级联赛(11场)、英超电竞(8场)

### HTML与API数据的对应关系

#### tid参数的实际含义
基于HTML分析，API #3中的`tid`参数应该对应这些数字ID：

```json
{
  "tid": "320",    // 印度卡纳塔克女子联赛
  "tid": "400",    // VS-PANDA独家EAFC24 欧洲冠军联赛
  "tid": "4780",   // 梦幻对垒
  "tid": "5940"    // 芬兰丁级联赛
}
```

#### 数据验证
HTML显示的赛事名称与API返回的数据高度一致：
- ✅ VS-PANDA独家EAFC24系列
- ✅ 各国低级别联赛
- ✅ 青年联赛和女子联赛
- ✅ 后备队联赛

### 滚球 vs 赛前数据分析

#### 比赛状态区分
基于HTML代码分析，平台明确区分了不同的比赛状态：

##### 赛前数据特征
```html
<div class="col-left">未开赛 <span class="match-number">194</span></div>
```
- **状态标识**: "未开赛"
- **比赛数量**: 194场
- **API对应**: `gcs = 0`
- **数据特点**: 完整的投注盘口，稳定的赔率

##### 滚球数据特征 (推测)
虽然当前HTML主要显示赛前数据，但可以推测滚球数据的特征：
- **状态标识**: 可能是"进行中"或"滚球"
- **API对应**: `gcs = 1` (进行中)
- **数据特点**: 实时更新的赔率，动态的盘口变化

#### API参数差异分析

##### 获取赛前数据
```json
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "",
  "apiType": 1,           // 可能1=赛前
  "orpt": 0,
  "euid": "3020101",
  "selectionHour": null
}
```

##### 获取滚球数据 (推测)
```json
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "",
  "apiType": 2,           // 可能2=滚球
  "orpt": 0,
  "euid": "3020101",
  "selectionHour": null
}
```

#### 数据获取策略建议

##### 1. 分别获取两种数据
- **赛前数据**: 用于分析和预测
- **滚球数据**: 用于实时监控和套利

##### 2. 状态筛选
- 通过`gcs`字段筛选不同状态的比赛
- `gcs = 0`: 赛前比赛
- `gcs = 1`: 进行中比赛 (滚球)
- `gcs = 2`: 已结束比赛

##### 3. 实时更新频率
- **赛前数据**: 相对稳定，可以较低频率更新
- **滚球数据**: 需要高频率实时更新

## � 文件说明和对应关系

### 文件对应关系表
| 文件名 | 对应API | 数据类型 | 说明 |
|--------|---------|----------|------|
| **1.md** | API #4 | 赛前比赛详情 | 包含完整的盘口和赔率数据 |
| **OB赛事HTML代码.md** | API #3 nolivedata | 赛前赛事列表 | 包含tid值和联赛信息的HTML源码 |
| **滚球赛事HTML源码.md** | API #3 livedata | 滚球比赛 | 包含进行中比赛的HTML源码 |
| **obwsjosn.md** | 未知 | 其他数据 | 待分析的JSON数据 |

### OB赛事HTML代码.md 详细说明

#### 文件内容概述
- **数据来源**: 赛前比赛列表页面的HTML源码
- **对应API**: API #3 nolivedata部分 (获取赛前赛事目录)
- **数据类型**: 赛前未开赛的比赛列表
- **关键信息**: tid值、联赛名称、比赛数量

#### 发现的重要tid值
从HTML代码中提取的实际tid参数：

| tid值 | 联赛名称 | 比赛数量 | 类型 |
|-------|----------|----------|------|
| **320** | 印度卡纳塔克女子联赛 | 1场 | 真实足球 |
| **360** | 澳门甲级联赛 - 附加赛 | 1场 | 真实足球 |
| **400** | VS-PANDA独家EAFC24 欧洲冠军联赛 | 1场 | 电竞足球 |
| **440** | VS-PANDA独家EAFC24 英格兰超级联赛 | 1场 | 电竞足球 |
| **480** | VS-PANDA独家EAFC24 德国甲级联赛 | 1场 | 电竞足球 |
| **4780** | 梦幻对垒 | 10场 | 虚拟比赛 |
| **5500** | 阿根廷联赛后备队 | 4场 | 后备队 |
| **5580** | 挪威联赛U19 | 5场 | 青年队 |
| **5620** | 爱沙尼亚联赛U19 | 1场 | 青年队 |
| **5820** | 洪都拉斯国家联赛后备队 | 1场 | 后备队 |
| **5860** | 印度杜兰德杯 | 1场 | 真实足球 |
| **5940** | 芬兰丁级联赛 | 11场 | 真实足球 |

#### HTML结构分析
```html
<!-- 联赛标题行 -->
<div style="position: absolute; color: red; z-index: 1; display: none;">
  455cff224257c882d9d0cd3ffab78d35- true- 320
</div>

<!-- 联赛信息 -->
<span class="ellipsis allow-user-select">印度卡纳塔克女子联赛</span>
<span class="league-match-count">1</span>  <!-- 比赛数量 -->
```

#### 关键发现
1. **tid值隐藏在HTML中**: 通过`display: none`的div元素存储
2. **比赛数量**: 通过`league-match-count`类显示
3. **联赛图标**: 每个联赛都有对应的图标URL
4. **状态标识**: "未开赛 194" 表示总共194场赛前比赛

#### 与API数据的关系
- **OB赛事HTML代码.md**: 提供tid参数值 → 对应API #3 nolivedata (赛前比赛)
- **滚球赛事HTML源码.md**: 滚球比赛信息 → 对应API #3 livedata (滚球比赛)
- **1.md**: API #4返回的具体比赛详情 → 包含盘口和赔率数据
- **数据流程**: HTML获取tid/mids → API #3获取比赛列表 → API #4获取比赛详情

## �🔴 滚球HTML源码分析

### 滚球赛事HTML源码.md 详细分析

#### 发现的滚球比赛信息
```html
<!-- 赛事名称和标识 -->
<tt id="lea_name">欧洲冠军联赛外围赛</tt><i class="icon_flag flag_EU"></i>

<!-- 比赛进行状态 -->
<i id="icon_info" class="">上半场<b></b><i class="txt_bk">33:56</i></i>

<!-- 球队信息 -->
<div class="box_team teamH"><span class="text_team strong_team">基辅迪纳摩</span></div>
<div class="box_team teamC"><span class="text_team ">帕福斯</span></div>

<!-- 实时比分 -->
<span class="text_point last_goal">0</span>  <!-- 主队比分 -->
<span class="text_point last_goal">0</span>  <!-- 客队比分 -->

<!-- 红牌统计 -->
<span class="icon_redcard teamH">0</span>    <!-- 主队红牌 -->
<span class="icon_redcard teamC">0</span>    <!-- 客队红牌 -->
```

#### 关键发现对比

##### 1. **比赛ID格式差异**
- **赛前比赛ID**: `4593188` (7位数字)
- **滚球比赛ID**: `9746807` (7位数字，但数值范围不同)
- **联赛ID**: `LEG_102416` (滚球联赛标识)

##### 2. **状态标识差异**
| 数据类型 | HTML显示 | API字段 | 说明 |
|----------|----------|---------|------|
| **赛前** | "未开赛 194" | `gcs: 0` | 静态状态 |
| **滚球** | "上半场 33:56" | `gcs: 1` | 动态时间 |

##### 3. **盘口类型差异**
```html
<!-- 滚球特有的半场盘口 -->
<span>让球 上半场</span>        <!-- 半场让球盘 -->
<span>得分大小 上半场</span>    <!-- 半场大小球盘 -->
```

##### 4. **滚球特有元素**
```html
<i id="icon_rb" class="icon_inplay">     <!-- 滚球进行中图标 -->
<span class="game_total">51</span>       <!-- 该比赛总盘口数量 -->
<span class="strong_team">基辅迪纳摩</span> <!-- 强队标识 -->
```

### 滚球数据获取策略

#### 推测的滚球API参数

##### API #3 获取滚球赛事 (推测)
```json
{
  "cuid": "523963395904882753",
  "sort": 1,
  "tid": "102416",              // 欧洲冠军联赛外围赛 (从HTML获取)
  "apiType": 2,                 // 推测: 2=滚球数据
  "orpt": 1,                    // 推测: 1=实时数据
  "euid": "3020101",
  "selectionHour": null
}
```

##### API #4 获取滚球比赛详情 (推测)
```json
{
  "mids": "9746807",            // 滚球比赛ID (从HTML获取)
  "cuid": "523963395904882753",
  "cos": 1,                     // 推测: 1=滚球模式
  "orpt": 1,                    // 推测: 1=实时更新
  "euid": "3020101"
}
```

#### 验证方法
1. **测试不同apiType值**: 0, 1, 2, 3 看哪个返回滚球数据
2. **测试cos参数**: 0, 1, 2 看是否影响数据类型
3. **使用HTML中的ID**: 尝试 `tid: "102416"` 和 `mids: "9746807"`

---

*文档创建时间: 2025年8月6日*
*最后更新: 2025年8月6日 - 🔍 添加HTML代码分析，发现tid参数规律*



