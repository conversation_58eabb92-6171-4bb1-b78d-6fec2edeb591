<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>HG滚球赛事监控</title>
  <!-- 引入CryptoJS库用于加密 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>

  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
      background-color: #f5f5f5;
      color: #333;
      padding: 20px;
      line-height: 1.6;
    }

    /* JSON树形结构样式 */
    .json-tree {
      font-family: 'Microsoft YaHei', sans-serif;
      line-height: 20px;
      font-size: 14px;
    }

    .json-node {
      position: relative;
      white-space: nowrap;
      padding: 1px 0;
    }

    .json-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      font-size: 10px;
      color: white;
      font-weight: bold;
      border-radius: 2px;
      margin-right: 4px;
      vertical-align: middle;
    }

    .json-icon.object { background-color: #3498db; } /* 蓝色 - 对象 */
    .json-icon.array { background-color: #e67e22; }  /* 橙色 - 数组 */
    .json-icon.string { background-color: #27ae60; } /* 绿色 - 字符串 */
    .json-icon.number { background-color: #9b59b6; } /* 紫色 - 数字 */
    .json-icon.boolean { background-color: #1abc9c; } /* 青色 - 布尔 */
    .json-icon.null { background-color: #8e44ad; }   /* 深紫 - null */

    .json-toggle {
      display: inline-block;
      width: 14px;
      height: 14px;
      text-align: center;
      line-height: 12px;
      font-size: 12px;
      cursor: pointer;
      margin-right: 2px;
      user-select: none;
      color: #666;
    }

    .json-toggle:hover {
      background-color: #f0f0f0;
      border-radius: 2px;
    }

    .json-key {
      color: #8e44ad;
      font-weight: 500;
    }

    .json-value {
      margin-left: 4px;
    }

    .json-value.string { color: #27ae60; }
    .json-value.number { color: #9b59b6; }
    .json-value.boolean { color: #1abc9c; }
    .json-value.null { color: #8e44ad; }

    .json-children {
      display: block;
    }

    .json-children.collapsed {
      display: none;
    }

    .json-count {
      color: #999;
      font-style: italic;
      margin-left: 4px;
    }

    /* 层级缩进 */
    .level-1 { padding-left: 12px; }
    .level-2 { padding-left: 44px; }
    .level-3 { padding-left: 76px; }
    .level-4 { padding-left: 108px; }
    .level-5 { padding-left: 140px; }
    .level-6 { padding-left: 172px; }
    .level-7 { padding-left: 204px; }
    .level-8 { padding-left: 236px; }
    .level-9 { padding-left: 268px; }
    .level-10 { padding-left: 300px; }
    .level-11 { padding-left: 332px; }
    .level-12 { padding-left: 364px; }
    .level-13 { padding-left: 396px; }
    .level-14 { padding-left: 428px; }
    .level-15 { padding-left: 460px; }

    .deep-level {
      padding-left: calc(var(--level) * 32px);
    }

    /* 连接线样式 */
    .json-node::before {
      content: '';
      position: absolute;
      left: calc(var(--level, 0) * 32px - 20px);
      top: 0;
      bottom: 0;
      width: 1px;
      background-color: #d0d0d0;
    }

    .json-node::after {
      content: '';
      position: absolute;
      left: calc(var(--level, 0) * 32px - 20px);
      top: 10px;
      width: 14px;
      height: 1px;
      background-color: #d0d0d0;
    }

    .json-node.last-child::before {
      bottom: 10px;
    }
    
    .container {
      width: 100%;
      margin: 0 auto;
      background-color: #fff;
      border: 1px solid #e6e6e6;
      padding: 15px;
      overflow: hidden;
    }
    
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 15px;
      font-weight: 500;
      font-size: 18px;
      border-bottom: 1px solid #e6e6e6;
      padding-bottom: 10px;
    }
    
    .form-container {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f9f9f9;
      border-radius: 6px;
      border: 1px solid #eee;
    }
    
    .form-container:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #555;
    }
    
    input[type="text"], textarea, input[type="number"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    input[type="text"]:focus, textarea:focus, input[type="number"]:focus {
      border-color: #4a6cf7;
      outline: none;
      box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
    }
    
    textarea {
      height: 100px;
      resize: vertical;
    }
    
    .refresh-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;
    }
    
    .refresh-interval {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .toggle-btn {
      background-color: #4a6cf7;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
    }
    
    .toggle-btn:hover {
      background-color: #3a56d5;
    }
    
    .status-bar {
      display: flex;
      justify-content: space-between;
      padding: 10px 15px;
      background-color: #f0f8ff;
      border-radius: 5px;
      margin-bottom: 20px;
      font-size: 14px;
      border: 1px solid #c6e2ff;
      align-items: center;
    }
    
    .status-indicator {
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .status-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      display: inline-block;
    }
    
    .status-dot.online {
      background-color: #28a745;
      box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
    }
    
    .status-dot.offline {
      background-color: #dc3545;
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
    }
    
    .status-dot.ws-online {
      background-color: #17a2b8;
      box-shadow: 0 0 0 2px rgba(23, 162, 184, 0.2);
      animation: pulsate 2s infinite;
    }
    
    .status-dot.ws-offline {
      background-color: #ffc107;
      box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
    }
    

    
    @keyframes pulsate {
      0% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.8; transform: scale(1.1); }
      100% { opacity: 1; transform: scale(1); }
    }
    
    .refresh-info {
      display: flex;
      gap: 15px;
      color: #666;
    }
    

    
    .loading {
      text-align: center;
      padding: 20px;
      font-size: 16px;
      color: #666;
    }
    
    .error {
      background-color: #f8d7da;
      color: #721c24;
      padding: 10px 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      border: 1px solid #f5c6cb;
      display: none;
    }
    
    .data-status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 5px;
    }
    
    .data-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      border: 1px solid #ddd;
      font-size: 11px; /* 更小的字体大小 */
      table-layout: fixed; /* 固定表格布局 */
    }
    
    .data-table thead {
      background-color: #f2f2f2;
      color: #333;
    }
    
    .data-table th {
      padding: 6px 4px; /* 减小表头内边距 */
      text-align: center; /* 表头居中 */
      font-weight: 500;
      position: sticky;
      top: 0;
      background-color: #f2f2f2;
      z-index: 10;
      font-size: 11px; /* 表头字体与内容一致 */
      white-space: nowrap; /* 防止表头文字换行 */
      border: 1px solid #ddd; /* Excel风格边框 */
    }
    
    .data-table tbody tr {
      border: 1px solid #ddd;
    }
    
    .data-table tbody tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    
    .data-table tbody tr:hover {
      background-color: #f5f5f5;
    }
    
    .data-table td {
      padding: 4px 5px; /* 减小单元格内边距 */
      border: 1px solid #ddd; /* Excel风格边框 */
      line-height: 1.3; /* 减小行高 */
      vertical-align: middle; /* 垂直居中 */
      word-break: break-word; /* 长文本自动换行 */
      font-size: 11px; /* 确保单元格内字体一致 */
    }
    
    /* 滚球时间样式 */
    .match-time {
      font-weight: 500;
      padding: 2px 6px;
      border-radius: 4px;
      display: inline-block;
    }
    
    .first-half {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      color: #0050b3;
    }
    
    .second-half {
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      color: #237804;
    }
    
    .half-time {
      background-color: #fff7e6;
      border: 1px solid #ffd591;
      color: #ad4e00;
      font-weight: bold;
    }
    
    .penalty {
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      color: #cf1322;
      font-weight: bold;
    }
    
    .first-half-cell, .second-half-cell, .half-time-cell, .penalty-cell {
      text-align: center;
    }
    
    /* 修改比分变化的闪烁效果 - 更加简洁 */
    .score-changed {
      background-color: #ffffcc;
    }
    
    #score-notifications {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      width: 300px;
    }
    
    .score-notification {
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      margin-bottom: 10px;
      padding: 12px 15px;
      border-left: 4px solid #4a6cf7;
      animation: slide-in 0.3s ease-out;
      transition: opacity 0.5s, transform 0.5s;
    }
    

    
    .score-details {
      display: flex;
      align-items: center;
      gap: 10px;
      margin: 8px 0;
    }
    
    .old-score {
      text-decoration: line-through;
      color: #999;
    }
    
    .arrow {
      color: #666;
    }
    
    .new-score {
      font-weight: bold;
      color: #e63946;
    }
    
    .match-name {
      font-size: 13px;
      color: #666;
    }
    
    .league-name {
      font-size: 12px;
      color: #0066cc;
      margin: 4px 0;
      font-weight: bold;
    }
    

    

    

    
    .no-data {
      text-align: center;
      padding: 30px 0;
      color: #666;
      font-size: 14px;
      font-style: italic;
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .container {
        padding: 15px;
      }
      
      .data-table {
        display: block;
        overflow-x: auto;
      }
      
      .refresh-controls {
        flex-direction: column;
        align-items: flex-start;
      }
      
      .status-bar {
        flex-direction: column;
        gap: 10px;
      }
      
      .form-container {
        padding: 10px;
      }
    }
    

    

    
    /* 针对赔率数据的样式 */
    .data-table td span.odds-item {
      display: block;
      white-space: nowrap;
      margin: 2px 0;
    }
    
    /* 让表格更紧凑并改善对齐 */
    .data-table th:nth-child(1), 
    .data-table td:nth-child(1) {
      width: 8%;
      text-align: left;
    }
    
    .data-table th:nth-child(2), 
    .data-table td:nth-child(2) {
      width: 6%;
      text-align: center;
    }
    
    /* 联赛列 */
    .data-table th:nth-child(3), 
    .data-table td:nth-child(3) {
      width: 80px;
    }
    
    /* 滚球时间 */
    .data-table th:nth-child(4), 
    .data-table td:nth-child(4) {
      width: 50px;
      text-align: center;
    }
    
    /* 主队和客队列宽度控制 */
    .data-table th:nth-child(5), 
    .data-table td:nth-child(5),
    .data-table th:nth-child(7), 
    .data-table td:nth-child(7) {
      width: 90px;
    }
    
    /* 比分列居中 */
    .data-table th:nth-child(6), 
    .data-table td:nth-child(6) {
      width: 40px;
      text-align: center;
    }
    
    /* 比赛时间列 */
    .data-table th:nth-child(8), 
    .data-table td:nth-child(8) {
      width: 90px;
      text-align: center;
    }
    
    /* 赔率列统一宽度 */
    .data-table th:nth-child(9), 
    .data-table td:nth-child(9),
    .data-table th:nth-child(10), 
    .data-table td:nth-child(10),
    .data-table th:nth-child(11), 
    .data-table td:nth-child(11),
    .data-table th:nth-child(12), 
    .data-table td:nth-child(12) {
      width: 120px;
      text-align: center;
    }

    /* 标签页样式 */
    .tabs-container {
      margin-bottom: 20px;
    }

    .tabs-nav {
      display: flex;
      border-bottom: 2px solid #e0e0e0;
      background-color: #f8f9fa;
      border-radius: 6px 6px 0 0;
    }

    .tab-button {
      padding: 12px 24px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
      flex: 1;
      text-align: center;
    }

    .tab-button:hover {
      background-color: #e9ecef;
      color: #333;
    }

    .tab-button.active {
      color: #007bff;
      border-bottom-color: #007bff;
      background-color: white;
    }

    .tab-content {
      display: none;
      padding: 20px;
      background-color: white;
      border: 1px solid #e0e0e0;
      border-top: none;
      border-radius: 0 0 6px 6px;
      min-height: 400px;
    }

    .tab-content.active {
      display: block;
    }

  </style>

  <!-- 整合的JS代码 -->
  <script>
/**
 * XML到JSON转换工具
 * 将POST请求类型1返回的XML数据转换成自定义JSON格式
 *
 * 用途：处理体育赛事数据，特别是足球比赛的实时信息和赔率数据
 * 输入：API返回的XML格式数据
 * 输出：结构化的JSON对象，便于前端展示和处理
 */

/**
 * 将POST请求类型1返回的XML数据转换成自定义JSON格式
 *
 * @param {string} xmlString - 从API返回的原始XML字符串
 * @returns {Object} - 转换后的JSON对象
 *
 * 此函数将足球比赛XML数据转换为结构化JSON格式:
 * {
 *   "标识": "HG",                          // 数据来源标识
 *   "UID": "",                            // 用户唯一标识
 *   "运动类型": "足球",                     // 运动类型
 *   "比赛类型": "赛前",                     // 比赛类型
 *   "matches": [
 *     {
 *       "id": "ec比赛ID",                    // 比赛唯一标识
 *       "league": "联赛名称",                // 例如: 澳大利亚杯外围赛
 *       "dateTime": "MM-DD HH:MM",          // 比赛时间，格式化为北京时间
 *       "teams": {
 *         "home": "主队名称",               // 主队全名
 *         "away": "客队名称"                // 客队全名
 *       },
 *       "score": "0-0",                     // 当前比分格式
 *       "status": "上半场/下半场 MM:SS",     // 比赛状态，包括当前半场和时间
 *       "odds": {
 *         "fullTimeHandicap": "-0.5[1.000];+0.5[0.880]<br>-0.75[1.000];+0.75[0.880]",      // 全场让球盘口，多个盘口用<br>连接
 *         "fullTimeOverUnder": "大2.5[0.990];小2.5[0.870]<br>大2.75[0.990];小2.75[0.870]",   // 全场大小球盘口，多个盘口用<br>连接
 *         "halfTimeHandicap": "-0.25[0.820];+0.25[1.060]<br>-0.5[0.820];+0.5[1.060]",    // 半场让球盘口，多个盘口用<br>连接
 *         "halfTimeOverUnder": "大1.25[0.910];小1.25[0.950]<br>大1.5[0.910];小1.5[0.950]"  // 半场大小球盘口，多个盘口用<br>连接
 *       }
 *     }
 *     // 更多比赛...
 *   ]
 * }
 */
function convertXmlToCustomJson(xmlString) {
  // 获取页面上的UID输入框值
  const customUidInput = document.getElementById('customUid');
  const uid = customUidInput ? customUidInput.value.trim() : '';

  // 创建结果对象
  const result = {
    标识: 'HG',
    UID: uid,
    运动类型: '足球',
    比赛类型: '赛前',
    matches: []
  };

  try {
    // 解析XML字符串为DOM对象
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlString, 'text/xml');

    // 获取所有比赛节点 <ec>
    const ecNodes = xmlDoc.querySelectorAll('ec');

    // 处理每个比赛节点
    ecNodes.forEach(ecNode => {
      // 获取比赛ID
      const ecId = ecNode.getAttribute('id');

      // 获取game节点(包含比赛数据)
      const gameNode = ecNode.querySelector('game');
      if (!gameNode) return; // 跳过没有game节点的ec

      // 提取基本比赛信息
      const leagueName = getNodeTextContent(gameNode, 'LEAGUE');
      const homeTeam = getNodeTextContent(gameNode, 'TEAM_H');
      const awayTeam = getNodeTextContent(gameNode, 'TEAM_C');
      const homeScore = getNodeTextContent(gameNode, 'SCORE_H');
      const awayScore = getNodeTextContent(gameNode, 'SCORE_C');
      const dateTime = getNodeTextContent(gameNode, 'DATETIME');
      const retimeSet = getNodeTextContent(gameNode, 'RETIMESET');

      // 解析时间信息
      const matchStatus = parseMatchTime(retimeSet);

      // 存储所有盘口数据的数组
      const fullTimeHandicaps = [];
      const fullTimeOverUnders = [];
      const halfTimeHandicaps = [];
      const halfTimeOverUnders = [];

      // 定义要处理的盘口类型 (主盘口、A盘口、B盘口)
      const handicapTypes = [
        { prefix: '', suffix: '' },           // 主盘口: RATIO_RE, IOR_REH, IOR_REC
        { prefix: 'A_sub_', suffix: '' },     // A盘口: A_sub_RATIO_RE, A_sub_IOR_REH, A_sub_IOR_REC
        { prefix: 'B_sub_', suffix: '' }      // B盘口: B_sub_RATIO_RE, B_sub_IOR_REH, B_sub_IOR_REC
      ];

      // 遍历所有盘口类型收集数据
      handicapTypes.forEach(handicapType => {
        const { prefix, suffix } = handicapType;

        // 提取赔率信息 - 全场让球盘
        const fullHandicapValue = getNodeTextContent(gameNode, `${prefix}RATIO_RE${suffix}`);
        const fullHomeOdds = getNodeTextContent(gameNode, `${prefix}IOR_REH${suffix}`);
        const fullAwayOdds = getNodeTextContent(gameNode, `${prefix}IOR_REC${suffix}`);
        const strongTeam = getNodeTextContent(gameNode, `${prefix}STRONG${suffix}`) || getNodeTextContent(gameNode, 'STRONG') || 'H';

        // 提取赔率信息 - 全场大小球
        const fullOuValue = getNodeTextContent(gameNode, `${prefix}RATIO_ROUO${suffix}`);
        const fullOverOdds = getNodeTextContent(gameNode, `${prefix}IOR_ROUC${suffix}`);
        const fullUnderOdds = getNodeTextContent(gameNode, `${prefix}IOR_ROUH${suffix}`);

        // 提取赔率信息 - 半场让球盘
        const halfHandicapValue = getNodeTextContent(gameNode, `${prefix}RATIO_HRE${suffix}`);
        const halfHomeOdds = getNodeTextContent(gameNode, `${prefix}IOR_HREH${suffix}`);
        const halfAwayOdds = getNodeTextContent(gameNode, `${prefix}IOR_HREC${suffix}`);
        const halfStrongTeam = getNodeTextContent(gameNode, `${prefix}HSTRONG${suffix}`) || strongTeam;

        // 提取赔率信息 - 半场大小球
        const halfOuValue = getNodeTextContent(gameNode, `${prefix}RATIO_HROUO${suffix}`);
        const halfOverOdds = getNodeTextContent(gameNode, `${prefix}IOR_HROUC${suffix}`);
        const halfUnderOdds = getNodeTextContent(gameNode, `${prefix}IOR_HROUH${suffix}`);

        // 格式化盘口信息并添加到数组（生成对象格式用于表格显示）
        if (fullHandicapValue && fullHomeOdds && fullAwayOdds) {
          let handicap = fullHandicapValue.trim().replace(/\s+/g, '');

          // 将分数形式转换为小数
          if (handicap.includes('/')) {
            const parts = handicap.split('/');
            if (parts.length === 2) {
              const num1 = parseFloat(parts[0]);
              const num2 = parseFloat(parts[1]);
              if (!isNaN(num1) && !isNaN(num2)) {
                handicap = ((num1 + num2) / 2).toFixed(2);
              }
            }
          }

          // 根据强队方向设置主队和客队盘口
          let homeHandicap, awayHandicap;
          if (handicap === "0") {
            homeHandicap = "0";
            awayHandicap = "0";
          } else if (strongTeam === 'H') {
            homeHandicap = `-${handicap}`;
            awayHandicap = `+${handicap}`;
          } else {
            homeHandicap = `+${handicap}`;
            awayHandicap = `-${handicap}`;
          }

          // 构建 homebetStr 和 awaybetStr
          const ecIdNum = ecId ? ecId.replace(/^ec/i, '') : '';
          const gid = getNodeTextContent(gameNode, `${prefix}GID${suffix}`) || getNodeTextContent(gameNode, 'GID') || '';
          const homebetStr = `${ecIdNum}!FT!${gid}!RE!REH!H`;
          const awaybetStr = `${ecIdNum}!FT!${gid}!RE!REC!C`;

          const fullHandicapObj = {
            homeHandicap,
            awayHandicap,
            homeOdds: fullHomeOdds,
            awayOdds: fullAwayOdds,
            homebetStr,
            awaybetStr
          };

          // 检查是否已存在相同的盘口
          if (!fullTimeHandicaps.some(h => h.homeHandicap === homeHandicap && h.awayHandicap === awayHandicap)) {
            fullTimeHandicaps.push(fullHandicapObj);
          }
        }

        if (fullOuValue && fullOverOdds && fullUnderOdds) {
          let total = fullOuValue.trim().replace(/\s+/g, '');

          // 将分数形式转换为小数
          if (total.includes('/')) {
            const parts = total.split('/');
            if (parts.length === 2) {
              const num1 = parseFloat(parts[0]);
              const num2 = parseFloat(parts[1]);
              if (!isNaN(num1) && !isNaN(num2)) {
                total = ((num1 + num2) / 2).toFixed(2);
              }
            }
          }

          // 构建 overbetStr 和 underbetStr
          const ecIdNum = ecId ? ecId.replace(/^ec/i, '') : '';
          const gid = getNodeTextContent(gameNode, `${prefix}GID${suffix}`) || getNodeTextContent(gameNode, 'GID') || '';
          const overbetStr = `${ecIdNum}!FT!${gid}!ROU!ROUC!C`;
          const underbetStr = `${ecIdNum}!FT!${gid}!ROU!ROUH!H`;

          // 为 total 添加 "大 " 和 "小 " 前缀
          const overTotal = `大 ${total}`;
          const underTotal = `小 ${total}`;

          const fullOUObj = {
            overTotal,
            underTotal,
            overOdds: fullOverOdds,
            underOdds: fullUnderOdds,
            overbetStr,
            underbetStr
          };

          // 检查是否已存在相同的盘口
          if (!fullTimeOverUnders.some(ou => ou.overTotal === overTotal && ou.underTotal === underTotal)) {
            fullTimeOverUnders.push(fullOUObj);
          }
        }

        if (halfHandicapValue && halfHomeOdds && halfAwayOdds) {
          let handicap = halfHandicapValue.trim().replace(/\s+/g, '');

          // 将分数形式转换为小数
          if (handicap.includes('/')) {
            const parts = handicap.split('/');
            if (parts.length === 2) {
              const num1 = parseFloat(parts[0]);
              const num2 = parseFloat(parts[1]);
              if (!isNaN(num1) && !isNaN(num2)) {
                handicap = ((num1 + num2) / 2).toFixed(2);
              }
            }
          }

          // 根据强队方向设置主队和客队盘口
          let homeHandicap, awayHandicap;
          if (handicap === "0") {
            homeHandicap = "0";
            awayHandicap = "0";
          } else if (halfStrongTeam === 'H') {
            homeHandicap = `-${handicap}`;
            awayHandicap = `+${handicap}`;
          } else {
            homeHandicap = `+${handicap}`;
            awayHandicap = `-${handicap}`;
          }

          // 构建半场 homebetStr 和 awaybetStr
          const ecIdNum = ecId ? ecId.replace(/^ec/i, '') : '';
          const hgid = getNodeTextContent(gameNode, `${prefix}HGID${suffix}`) || getNodeTextContent(gameNode, 'HGID') || '';
          const homebetStr = `${ecIdNum}!FT!${hgid}!HRE!HREH!H`;
          const awaybetStr = `${ecIdNum}!FT!${hgid}!HRE!HREC!C`;

          const halfHandicapObj = {
            homeHandicap,
            awayHandicap,
            homeOdds: halfHomeOdds,
            awayOdds: halfAwayOdds,
            homebetStr,
            awaybetStr
          };

          // 检查是否已存在相同的盘口
          if (!halfTimeHandicaps.some(h => h.homeHandicap === homeHandicap && h.awayHandicap === awayHandicap)) {
            halfTimeHandicaps.push(halfHandicapObj);
          }
        }

        if (halfOuValue && halfOverOdds && halfUnderOdds) {
          let total = halfOuValue.trim().replace(/\s+/g, '');

          // 将分数形式转换为小数
          if (total.includes('/')) {
            const parts = total.split('/');
            if (parts.length === 2) {
              const num1 = parseFloat(parts[0]);
              const num2 = parseFloat(parts[1]);
              if (!isNaN(num1) && !isNaN(num2)) {
                total = ((num1 + num2) / 2).toFixed(2);
              }
            }
          }

          // 构建半场 overbetStr 和 underbetStr
          const ecIdNum = ecId ? ecId.replace(/^ec/i, '') : '';
          const hgid = getNodeTextContent(gameNode, `${prefix}HGID${suffix}`) || getNodeTextContent(gameNode, 'HGID') || '';
          const overbetStr = `${ecIdNum}!FT!${hgid}!HROU!HROUC!C`;
          const underbetStr = `${ecIdNum}!FT!${hgid}!HROU!HROUH!H`;

          // 为 total 添加 "大 " 和 "小 " 前缀
          const overTotal = `大 ${total}`;
          const underTotal = `小 ${total}`;

          const halfOUObj = {
            overTotal,
            underTotal,
            overOdds: halfOverOdds,
            underOdds: halfUnderOdds,
            overbetStr,
            underbetStr
          };

          // 检查是否已存在相同的盘口
          if (!halfTimeOverUnders.some(ou => ou.overTotal === overTotal && ou.underTotal === underTotal)) {
            halfTimeOverUnders.push(halfOUObj);
          }
        }
      });

      // 格式化日期时间 (转换为北京时间)
      const formattedDateTime = formatDateTime(dateTime);

      // 格式化比分
      const formattedScore = `${homeScore}-${awayScore}`;

      // 创建比赛对象（直接使用数组格式，供表格显示使用）
      const match = {
        id: `${ecId}`,
        league: leagueName,
        dateTime: formattedDateTime,
        teams: {
          home: homeTeam,
          away: awayTeam
        },
        score: formattedScore,
        status: matchStatus,
        fullTimeHandicap: fullTimeHandicaps,
        fullTimeOverUnder: fullTimeOverUnders,
        halfTimeHandicap: halfTimeHandicaps,
        halfTimeOverUnder: halfTimeOverUnders
      };

      // 将比赛添加到结果数组
      result.matches.push(match);
    });

    console.log('XML转换完成，共处理比赛数:', result.matches.length);

    // 调试信息：输出第一个比赛的盘口数据结构
    if (result.matches.length > 0) {
      const firstMatch = result.matches[0];
      console.log('第一个比赛的盘口数据结构:');
      console.log('全场让球:', firstMatch.fullTimeHandicap);
      console.log('全场大小:', firstMatch.fullTimeOverUnder);
      console.log('半场让球:', firstMatch.halfTimeHandicap);
      console.log('半场大小:', firstMatch.halfTimeOverUnder);
    }

    return result;
  } catch (error) {
    console.error('XML转JSON出错:', error);
    return { matches: [], error: error.message };
  }
}

/**
 * 工具函数: 从节点获取文本内容
 * @param {Element} parentNode - 父节点
 * @param {string} tagName - 标签名
 * @returns {string} 文本内容或空字符串
 */
function getNodeTextContent(parentNode, tagName) {
  const node = parentNode.querySelector(tagName);
  return node ? node.textContent : '';
}

/**
 * 工具函数: 解析比赛时间信息
 * @param {string} retimeSet - 比赛时间字符串，格式如 "1H^35:07" 表示上半场35分07秒
 * @returns {string} 格式化的比赛状态字符串
 *
 * 支持以下格式:
 * - "1H^35:07" -> "上半场 35:07"
 * - "2H^12:30" -> "下半场 12:30"
 * - "MTIME^HT" 或 "HT" -> "中场休息"
 * - "MTIME^FT" -> "比赛结束"
 * - "MTIME^PT" -> "点球大战"
 */
function parseMatchTime(retimeSet) {
  if (!retimeSet) return "未开始";

  // 处理MTIME HT（中场休息）情况
  if (retimeSet.includes('MTIME^HT') || retimeSet === 'HT') {
    return "中场休息";
  }

  // 处理MTIME FT（全场结束）情况
  if (retimeSet.includes('MTIME^FT')) {
    return "比赛结束";
  }

  // 处理MTIME PT（点球）情况
  if (retimeSet.includes('MTIME^PT')) {
    return "点球大战";
  }

  // 按^分割，获取半场标识和时间
  const parts = retimeSet.split('^');
  if (parts.length !== 2) return retimeSet; // 格式不正确时直接返回原始值

  const halfIndicator = parts[0]; // 1H或2H
  const timeStr = parts[1];       // MM:SS

  // 转换半场标识
  let halfText = '';
  if (halfIndicator === '1H') {
    halfText = '上半场';
  } else if (halfIndicator === '2H') {
    halfText = '下半场';
  } else {
    halfText = halfIndicator;
  }

  return `${halfText} ${timeStr}`;
}

/**
 * 工具函数: 格式化日期时间
 * @param {string} dateTime - 原始日期时间字符串，格式如 "05-13 05:00a"
 * @returns {string} 转换为北京时间的字符串，格式如 "05-13 17:00"
 *
 * 将美东时间(UTC-4)转换为北京时间(UTC+8)，即:
 * - 美东时间 + 12小时 = 北京时间
 * - 处理am/pm标记，转换为24小时制
 */
function formatDateTime(dateTime) {
  if (!dateTime) return "";

  // 提取日期和时间
  const match = dateTime.match(/(\d{2}-\d{2})\s+(\d{1,2}):(\d{2})([ap])/);
  if (!match) return dateTime;

  const date = match[1];
  let hours = parseInt(match[2]);
  const minutes = match[3];
  const ampm = match[4];

  // 调整为24小时制
  if (ampm === 'p' && hours < 12) {
    hours += 12;
  } else if (ampm === 'a' && hours === 12) {
    hours = 0;
  }

  // 转换为北京时间 (UTC+8，假设原始时间是美东时间UTC-4)
  hours = (hours + 12) % 24; // 美东时间+12小时=北京时间

  // 格式化小时为两位数
  const formattedHours = hours.toString().padStart(2, '0');

  return `${date} ${formattedHours}:${minutes}`;
}

/**
 * 工具函数: 格式化让球盘口
 * @param {string} handicapValue - 让球值，如 "0.5"、"0.5 / 1" 或 "0"
 * @param {string} homeOdds - 主队赔率
 * @param {string} awayOdds - 客队赔率
 * @param {string} strong - 强队方向，'H'为主队，'C'为客队
 * @returns {string} 格式化的让球盘口字符串
 *
 * 输出格式:
 * - 主队让分: "-0.5[1.00];+0.5[0.88]" (负号表示让分，主队赔率和客队赔率)
 * - 客队让分: "+0.5[1.00];-0.5[0.88]" (正号表示受让，主队赔率和客队赔率)
 * - 平手盘: "0[1.00];0[0.88]" (无让分，主队赔率和客队赔率)
 */
function formatHandicap(handicapValue, homeOdds, awayOdds, strong = 'H') {
  // 检查数据完整性
  if (!handicapValue || !homeOdds || !awayOdds) {
    return "";
  }

  // 数据有效性检查 - 检查是否都是有效数值
  if (isNaN(parseFloat(homeOdds)) || isNaN(parseFloat(awayOdds))) {
    return "";
  }

  // 去除赔率中可能的尾随零
  const formattedHomeOdds = parseFloat(homeOdds).toString();
  const formattedAwayOdds = parseFloat(awayOdds).toString();

  // 让球值格式化 (处理特殊盘口格式)
  let handicap = handicapValue.trim();

  // 去除空格
  handicap = handicap.replace(/\s+/g, '');

  // 将分数形式转换为小数
  if (handicap.includes('/')) {
    const parts = handicap.split('/');
    if (parts.length === 2) {
      const num1 = parseFloat(parts[0]);
      const num2 = parseFloat(parts[1]);
      if (!isNaN(num1) && !isNaN(num2)) {
        handicap = ((num1 + num2) / 2).toFixed(2);
      }
    }
  }

  // 如果让球值为0，直接返回平手盘
  if (handicap === "0") {
    return `0[${formattedHomeOdds}];0[${formattedAwayOdds}]`;
  }

  // 根据强队方向确定正负号
  if (strong === 'H') {
    // 主队是强队，让分用负号
    return `-${handicap}[${formattedHomeOdds}];+${handicap}[${formattedAwayOdds}]`;
  } else if (strong === 'C') {
    // 客队是强队，主队受让用正号
    return `+${handicap}[${formattedHomeOdds}];-${handicap}[${formattedAwayOdds}]`;
  } else {
    // 无强队信息，默认主队让分
    return `-${handicap}[${formattedHomeOdds}];+${handicap}[${formattedAwayOdds}]`;
  }
}

/**
 * 工具函数: 格式化大小球盘口
 * @param {string} ouValue - 大小球值，如 "2.5" 或 "2.5 / 3"
 * @param {string} overOdds - 大球赔率
 * @param {string} underOdds - 小球赔率
 * @returns {string} 格式化的大小球盘口字符串
 *
 * 输出格式: "大2.5[0.99];小2.5[0.87]"
 * - 第一部分: 大球赔率，格式为"大[值][赔率]"
 * - 第二部分: 小球赔率，格式为"小[值][赔率]"
 *
 * 注意: 特殊盘口值如 "2.5 / 3" 会被转换为平均值 "2.75"
 */
function formatOverUnder(ouValue, overOdds, underOdds) {
  // 检查数据完整性
  if (!ouValue || !overOdds || !underOdds) {
    return "";
  }

  // 数据有效性检查 - 检查是否都是有效数值
  if (isNaN(parseFloat(overOdds)) || isNaN(parseFloat(underOdds))) {
    return "";
  }

  // 去除赔率中可能的尾随零
  const formattedOverOdds = parseFloat(overOdds).toString();
  const formattedUnderOdds = parseFloat(underOdds).toString();

  // 大小球值格式化 (处理特殊盘口格式)
  let ou = ouValue.trim();

  // 去除空格
  ou = ou.replace(/\s+/g, '');

  // 将分数形式转换为小数
  if (ou.includes('/')) {
    const parts = ou.split('/');
    if (parts.length === 2) {
      const num1 = parseFloat(parts[0]);
      const num2 = parseFloat(parts[1]);
      if (!isNaN(num1) && !isNaN(num2)) {
        ou = ((num1 + num2) / 2).toFixed(2);
      }
    }
  }

  return `大${ou}[${formattedOverOdds}];小${ou}[${formattedUnderOdds}]`;
}
  </script>
</head>
<body>
  <div class="container">
    <h1>HG滚球赛事监控</h1>

    <!-- 标签页导航 -->
    <div class="tabs-container">
      <div class="tabs-nav">
        <button class="tab-button active" onclick="switchTab('api-config')">API配置</button>
        <button class="tab-button" onclick="switchTab('data-convert')">数据转换</button>
        <button class="tab-button" onclick="switchTab('data-table')">数据表格</button>
      </div>

      <!-- API配置标签页 -->
      <div id="api-config" class="tab-content active">
        <!-- 用户配置表单区域
          - apiUrl: 设置API请求地址，默认为HG体育API
          - customUid: 设置自定义UID用于API认证
          - 自动刷新控制: 开启/关闭自动刷新，设置刷新间隔
        -->
        <div class="form-container">
      <div class="form-group">
        <label for="apiUrl">API请求地址:</label>
        <input type="text" id="apiUrl" name="apiUrl" value="https://m061.mos077.com/transform.php?ver=2025-05-15-C1bug_92" placeholder="请输入API请求地址">
      </div>
      <div class="form-group">
        <label for="customUid">UID:</label>
        <input type="text" id="customUid" name="customUid" value="yxsosgcm37418875l5644882b1" placeholder="请输入自定义UID">
      </div>

      <div class="refresh-controls">
        <button type="button" id="toggleAutoRefresh" class="toggle-btn">停止自动刷新</button>
        <div class="refresh-interval">
          <input type="number" id="refreshInterval" min="1000" step="1000" value="2500" placeholder="刷新间隔(毫秒)">
          <button type="button" id="applyInterval" class="toggle-btn">应用</button>
        </div>
      </div>
    </div>
    
    <!-- 状态栏区域 -->
    <div class="status-bar">
      <div class="status-indicator">
        <div class="status-dot online" id="status-dot"></div>
        <span id="connection-status">已连接</span>
      </div>
      <div class="status-indicator">
        <div class="status-dot ws-online" id="ws-status-dot"></div>
        <span id="ws-connection-status">WS已连接</span>
      </div>
      <div class="refresh-info">
        <span id="last-update">上次更新: --:--:--</span>
        <span id="next-update">下次更新: --:--:--</span>
      </div>
        </div>

        <div id="error" class="error"></div>
      </div> <!-- 关闭API配置标签页 -->

      <!-- 数据转换标签页 -->
      <div id="data-convert" class="tab-content">
        <!-- XML转JSON区域 -->
        <div class="xml-json-converter" style="margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 6px; border: 1px solid #eee;">
          <h3 style="margin-bottom: 15px; color: #333; font-size: 16px;">XML数据转JSON数据</h3>

          <!-- XML输入框 -->
          <div style="margin-bottom: 15px;">
            <label for="xmlInput" style="display: block; margin-bottom: 5px; font-weight: 500; color: #555;">XML数据输入:</label>
            <textarea id="xmlInput" placeholder="请输入XML数据..." style="width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; resize: vertical; font-family: 'Consolas', 'Monaco', monospace; white-space: pre; overflow-wrap: break-word;"></textarea>
          </div>

          <!-- JSON树形输出框 -->
          <div>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
              <label style="font-weight: 500; color: #555;">JSON数据输出:</label>
              <div style="display: flex; gap: 5px;">
                <button id="expandAllBtn" style="padding: 2px 8px; font-size: 12px; border: 1px solid #ddd; background: #f8f9fa; border-radius: 3px; cursor: pointer;" title="展开全部">⊞全部</button>
                <button id="collapseAllBtn" style="padding: 2px 8px; font-size: 12px; border: 1px solid #ddd; background: #f8f9fa; border-radius: 3px; cursor: pointer;" title="折叠全部">⊟全部</button>
                <button id="copyJsonBtn" style="padding: 2px 8px; font-size: 12px; border: 1px solid #ddd; background: #f8f9fa; border-radius: 3px; cursor: pointer;" title="复制JSON">📋复制</button>
                <button id="exportJsonBtn" style="padding: 2px 8px; font-size: 12px; border: 1px solid #ddd; background: #f8f9fa; border-radius: 3px; cursor: pointer;" title="导出JSON文件">💾导出</button>
              </div>
            </div>
            <div id="jsonTreeContainer" style="width: 100%; height: 150px; border: 1px solid #ddd; border-radius: 4px; background: white; overflow: auto; font-family: 'Microsoft YaHei', sans-serif; font-size: 14px; line-height: 20px; padding: 5px;">
              <div style="color: #999; text-align: center; padding: 20px;">转换后的JSON数据将显示在这里...</div>
            </div>
          </div>
        </div>
      
      <!-- JSON格式说明区域 -->
      <div style="margin-top: 15px;">
        <label for="jsonFormat" style="display: block; margin-bottom: 5px; font-weight: 500; color: #555;">JSON格式说明:</label>
        <textarea id="jsonFormat" style="width: 100%; height: 200px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 13px; resize: vertical; font-family: 'Consolas', 'Monaco', monospace; white-space: pre; overflow-wrap: break-word; background-color: #f8f9fa; color: #666;" readonly>{
  "标识": "HG",                             // 数据来源标识，固定为"HG"
  "UID": "yxsosgcm37418875l5644882b1",      // 用户唯一标识，来自页面UID输入框
  "运动类型": "足球",                        // 运动类型，例如：足球
  "比赛类型": "赛前",                        // 比赛类型，例如：赛前
  "matches": [                             // 比赛数组，包含所有转换后的比赛数据
    {
      "id": "ec123456",                    // 比赛唯一标识，从XML的<ec id="">中提取
      "league": "英格兰超级联赛",            // 联赛名称，从<LEAGUE>标签中提取
      "dateTime": "01-15 20:30",           // 比赛时间，从<DATETIME>转换为北京时间(MM-DD HH:MM)
      "teams": {                           // 参赛队伍信息
        "home": "曼彻斯特联",              // 主队名称，从<TEAM_H>标签中提取
        "away": "利物浦"                   // 客队名称，从<TEAM_C>标签中提取
      },
      "score": "1-0",                      // 当前比分，格式为"主队得分-客队得分"
      "status": "上半场 35:20",            // 比赛状态，从<RETIMESET>解析(如"1H^35:20"转换为"上半场 35:20")
      "fullTimeHandicap": [                // 全场让球盘口组
        {
          "homebetStr": "9746797!FT!8035517!RE!REH!H",    // 主队投注字符串，格式：ecId!FT!gid!RE!REH!H
                                                          // ├─ ecId: 从<ec id="">中提取，只保留数字部分
                                                          // ├─ FT: 固定值，表示全场
                                                          // ├─ gid: 从<GID>标签中提取
                                                          // ├─ RE: 固定值，表示让球盘
                                                          // ├─ REH: 固定值，表示主队让球
                                                          // └─ H: 固定值，表示主队
          "homeHandicap": "-0.5",                      // 主队让球值，负数表示主队让球
          "homeOdds": "1.95",                          // 主队赔率，从XML的<IOR_REH>标签中提取
          "awaybetStr": "9746797!FT!8035517!RE!REC!C", // 客队投注字符串，格式：ecId!FT!gid!RE!REC!C
                                                          // ├─ ecId: 从<ec id="">中提取，只保留数字部分
                                                          // ├─ FT: 固定值，表示全场
                                                          // ├─ gid: 从<GID>标签中提取
                                                          // ├─ RE: 固定值，表示让球盘
                                                          // ├─ REC: 固定值，表示客队让球
                                                          // └─ C: 固定值，表示客队
          "awayHandicap": "+0.5",                      // 客队受让值，正数表示客队受让
          "awayOdds": "1.85"                           // 客队赔率，从XML的<IOR_REC>标签中提取
        },
        {
          "gid": "002",                    // 另一个盘口的GID
          "homeHandicap": "-1",            // 主队让球值
          "homeOdds": "2.10",              // 主队赔率
          "awayHandicap": "+1",            // 客队受让值
          "awayOdds": "1.70"               // 客队赔率
        }
      ],
      "fullTimeOverUnder": [               // 全场大小球盘口组
        {
          "overbetStr": "9746797!FT!8035517!ROU!ROUC!C",  // 大球投注字符串，格式：ecId!FT!gid!ROU!ROUC!C
                                                          // ├─ ecId: 从<ec id="">中提取，只保留数字部分
                                                          // ├─ FT: 固定值，表示全场
                                                          // ├─ gid: 从<GID>标签中提取
                                                          // ├─ ROU: 固定值，表示大小球盘
                                                          // ├─ ROUC: 固定值，表示大球
                                                          // └─ C: 固定值，表示大球方向
          "overTotal": "大 2.5",                       // 大球盘口值，前缀"大 "加上从XML的<RATIO_ROUO>标签中提取的值
          "overOdds": "1.90",                          // 大球赔率，从XML的<IOR_ROUC>标签中提取
          "underbetStr": "9746797!FT!8035517!ROU!ROUH!H", // 小球投注字符串，格式：ecId!FT!gid!ROU!ROUH!H
                                                          // ├─ ecId: 从<ec id="">中提取，只保留数字部分
                                                          // ├─ FT: 固定值，表示全场
                                                          // ├─ gid: 从<GID>标签中提取
                                                          // ├─ ROU: 固定值，表示大小球盘
                                                          // ├─ ROUH: 固定值，表示小球
                                                          // └─ H: 固定值，表示小球方向
          "underTotal": "小 2.5",                      // 小球盘口值，前缀"小 "加上从XML的<RATIO_ROUO>标签中提取的值
          "underOdds": "1.90"                          // 小球赔率，从XML的<IOR_ROUH>标签中提取
        },
        {
          "gid": "004",                    // 另一个盘口的GID
          "total": "3",                    // 另一个大小球值
          "overOdds": "2.05",              // 大球赔率
          "underOdds": "1.75"              // 小球赔率
        }
      ],
      "halfTimeHandicap": [                // 半场让球盘口组
        {
          "homebetStr": "9746807!FT!8035528!HRE!HREH!H",   // 主队半场投注字符串，格式：ecId!FT!hgid!HRE!HREH!H
                                                          // ├─ ecId: 从<ec id="">中提取，只保留数字部分
                                                          // ├─ FT: 固定值，表示全场（半场也使用FT）
                                                          // ├─ hgid: 从<HGID>标签中提取（半场专用GID）
                                                          // ├─ HRE: 固定值，表示半场让球盘
                                                          // ├─ HREH: 固定值，表示半场主队让球
                                                          // └─ H: 固定值，表示主队
          "homeHandicap": "-0.25",                     // 主队半场让球值，从XML的<RATIO_HRE>标签中提取
          "homeOdds": "1.85",                          // 主队半场赔率，从XML的<IOR_HREH>标签中提取
          "awaybetStr": "9746807!FT!8035528!HRE!HREC!C", // 客队半场投注字符串，格式：ecId!FT!hgid!HRE!HREC!C
                                                          // ├─ ecId: 从<ec id="">中提取，只保留数字部分
                                                          // ├─ FT: 固定值，表示全场（半场也使用FT）
                                                          // ├─ hgid: 从<HGID>标签中提取（半场专用GID）
                                                          // ├─ HRE: 固定值，表示半场让球盘
                                                          // ├─ HREC: 固定值，表示半场客队让球
                                                          // └─ C: 固定值，表示客队
          "awayHandicap": "+0.25",                     // 客队半场受让值
          "awayOdds": "1.95"                           // 客队半场赔率，从XML的<IOR_HREC>标签中提取
        },
        {
          "gid": "006",                    // 另一个半场盘口的GID
          "homeHandicap": "0",             // 平手盘
          "homeOdds": "1.90",              // 主队赔率
          "awayHandicap": "0",             // 平手盘
          "awayOdds": "1.90"               // 客队赔率
        }
      ],
      "halfTimeOverUnder": [               // 半场大小球盘口组
        {
          "overbetStr": "9746807!FT!8035528!HROU!HROUC!C", // 半场大球投注字符串，格式：ecId!FT!hgid!HROU!HROUC!C
                                                          // ├─ ecId: 从<ec id="">中提取，只保留数字部分
                                                          // ├─ FT: 固定值，表示全场（半场也使用FT）
                                                          // ├─ hgid: 从<HGID>标签中提取（半场专用GID）
                                                          // ├─ HROU: 固定值，表示半场大小球盘
                                                          // ├─ HROUC: 固定值，表示半场大球
                                                          // └─ C: 固定值，表示大球方向
          "overTotal": "大 1",                         // 半场大球盘口值，前缀"大 "加上从XML的<RATIO_HROUO>标签中提取的值
          "overOdds": "1.80",                          // 半场大球赔率，从XML的<IOR_HROUC>标签中提取
          "underbetStr": "9746807!FT!8035528!HROU!HROUH!H", // 半场小球投注字符串，格式：ecId!FT!hgid!HROU!HROUH!H
                                                          // ├─ ecId: 从<ec id="">中提取，只保留数字部分
                                                          // ├─ FT: 固定值，表示全场（半场也使用FT）
                                                          // ├─ hgid: 从<HGID>标签中提取（半场专用GID）
                                                          // ├─ HROU: 固定值，表示半场大小球盘
                                                          // ├─ HROUH: 固定值，表示半场小球
                                                          // └─ H: 固定值，表示小球方向
          "underTotal": "小 1",                        // 半场小球盘口值，前缀"小 "加上从XML的<RATIO_HROUO>标签中提取的值
          "underOdds": "2.00"                          // 半场小球赔率，从XML的<IOR_HROUH>标签中提取
        },
        {
          "gid": "008",                    // 另一个半场盘口的GID
          "total": "1.5",                  // 另一个半场大小球值
          "overOdds": "2.10",              // 大球赔率
          "underOdds": "1.70"              // 小球赔率
        }
      ]
    }
    // 更多比赛数据...每个比赛都遵循相同的JSON结构
  ]
}</textarea>
        </div>
      </div>
    </div> <!-- 关闭数据转换标签页 -->

    <!-- 数据表格标签页 -->
    <div id="data-table" class="tab-content">
      <!-- 表格容器
        - 动态生成的比赛数据表格
        - 包含序号、ECID、联赛、比赛时间、队伍、比分等信息
        - 比分变化时会高亮显示
      -->
      <div id="tableContainer">
        <!-- 表格将在这里动态生成 -->
      </div>
    </div> <!-- 关闭数据表格标签页 -->

  </div> <!-- 关闭标签页容器 -->
  </div> <!-- 关闭主容器 -->




  <script>
          // 全局定义displayDataTable函数
      window.displayDataTable = function(hgData) {
      const tableContainer = document.getElementById('tableContainer');
      if (!tableContainer) {
        console.warn('表格容器元素不存在，无法显示数据');
        return;
      }
      
      console.log('显示数据表格...');
      
      // 这里只是一个简单的显示函数，不包含实际数据处理逻辑
      
      // 如果有全局事件委托处理已经设置，就不需要再次添加
      console.log('数据表格显示完成');
    };
    
    // 定义全局fetchData函数
    window.fetchData = function() {
      // 创建变量存储转换后的数据
      let hgData = { matches: [] };
      // 如果DOMContentLoaded已经触发，可以直接查找apiUrlInput等元素
      const apiUrlInput = document.getElementById('apiUrl');
      const customUidInput = document.getElementById('customUid');
      const tableContainer = document.getElementById('tableContainer');
      
      // 当前状态
      const statusDot = document.getElementById('status-dot');
      const connectionStatus = document.getElementById('connection-status');
      const lastUpdate = document.getElementById('last-update');
      const nextUpdate = document.getElementById('next-update');
      

      
      // 如果页面尚未准备好，则返回
      if (!apiUrlInput || !tableContainer) {
        console.warn('页面元素尚未准备好，无法获取数据');
        return;
      }
      
      try {
        // 设置连接状态为在线
        if (statusDot) statusDot.className = 'status-dot online';
        if (connectionStatus) connectionStatus.textContent = '已连接';
        
        // 获取API请求地址和UID
        const apiUrl = apiUrlInput.value.trim() || 'https://m6686.com/transform.php';
        
        // 获取自定义UID
        const customUid = customUidInput.value.trim();
        
        // 从API URL提取ver参数
        let ver = '';
        try {
          const urlObj = new URL(apiUrl);
          ver = urlObj.searchParams.get('ver') || '';
        } catch (e) {
          console.warn('无法从URL提取ver参数，使用默认值');
          ver = '';
        }
        
        // 构建POST数据
        let postData = '';
        
        // 使用输入框当前值，不从localStorage加载
        postData = `uid=${customUid || 'ne4ko3ewqm37418875l5535514b1'}&ver=${ver}&langx=zh-cn&p=get_game_list&p3type=&date=&gtype=ft&showtype=live&rtype=rrnou&ltype=3&filter=&cupFantasy=N&sorttype=L&specialClick=&isFantasy=N`;
        //          uid=w86g966am37976245l6537270b1                   &ver=2025  &langx=zh-cn&p=get_game_list&p3type=&date=&gtype=ft&showtype=live&rtype=rrnou&ltype=3&filter=&cupFantasy=N&sorttype=L&specialClick=&isFantasy=N&ts=1752756804956
        // 添加时间戳到请求中，确保每次请求都是唯一的
        const timestamp = Date.now();
        if (!postData.includes('ts=')) {
          postData = postData + '&ts=' + timestamp;
        } else {
          postData = postData.replace(/ts=\d+/, 'ts=' + timestamp);
        }
        
        // 输出请求信息
        console.log('执行数据获取请求...');
        
        // 模拟fetch操作
        console.log('模拟数据获取完成');
        
        // 更新最后一次刷新时间
        const now = new Date();
        if (lastUpdate) {
          lastUpdate.textContent = `上次更新: ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
        }
        
        if (nextUpdate) {
          nextUpdate.textContent = '下次更新: 已手动刷新';
        }
        
                  // 如果页面已加载完成，刷新表格
          if (typeof displayDataTable === 'function') {
            displayDataTable(hgData);
          } else {
            console.warn('displayDataTable函数尚未定义，无法刷新表格');
          }
      } catch (err) {
        console.error('全局fetchData函数出错:', err);
        // 设置连接状态为离线
        if (statusDot) statusDot.className = 'status-dot offline';
        if (connectionStatus) connectionStatus.textContent = '连接断开';
      }
    };
    
    window.setConnectionStatus = function(isConnected) {
      const statusDot = document.getElementById('status-dot');
      if (statusDot) {
        statusDot.className = isConnected ? 'status-dot online' : 'status-dot offline';
      }
      
      const statusText = document.getElementById('connection-status');
      if (statusText) {
        statusText.textContent = isConnected ? '已连接' : '连接断开';
      }
    };
    
    window.showMessage = function(message, isError = false) {
      const messageDiv = document.createElement('div');
      messageDiv.className = isError ? 'error-message' : 'success-message';
      messageDiv.textContent = message;
      messageDiv.style.position = 'fixed';
      messageDiv.style.top = '10px';
      messageDiv.style.left = '50%';
      messageDiv.style.transform = 'translateX(-50%)';
      messageDiv.style.padding = '10px 15px';
      messageDiv.style.borderRadius = '4px';
      messageDiv.style.zIndex = '1000';
      
      if (isError) {
        messageDiv.style.backgroundColor = '#f8d7da';
        messageDiv.style.border = '1px solid #f5c6cb';
        messageDiv.style.color = '#721c24';
      } else {
        messageDiv.style.backgroundColor = '#d4edda';
        messageDiv.style.border = '1px solid #c3e6cb';
        messageDiv.style.color = '#155724';
      }
      
      document.body.appendChild(messageDiv);
      
      setTimeout(() => {
        if (document.body.contains(messageDiv)) {
          document.body.removeChild(messageDiv);
        }
      }, 3000);
    };
  
    // WebSocket连接相关变量和函数
    let socket = null;
    let wsConnected = false;
    let wsReconnectTimer = null;
    const WS_SERVER_URL = 'ws://47.121.129.103:6080';
    const WS_RECONNECT_INTERVAL = 3000; // 重连间隔：3秒
    
    // 通用数据处理函数 - 不再使用加密
    function encryptData(data) {
      try {
        return typeof data === 'string' ? data : JSON.stringify(data);
      } catch (error) {
        console.error('数据处理失败:', error);
        return null;
      }
    }
    
    // 初始化WebSocket连接
    function initWebSocket() {
      if (socket) {
        // 清理之前的连接
        socket.onclose = null;
        socket.onerror = null;
        socket.onmessage = null;
        socket.onopen = null;
        socket.close();
        socket = null;
      }
      
      // 更新WebSocket状态UI
      updateWsStatus(false);
      
      try {
        console.log('正在连接WebSocket服务器:', WS_SERVER_URL);
        socket = new WebSocket(WS_SERVER_URL);
        
        // 连接成功事件
        socket.onopen = function() {
          console.log('WebSocket连接成功');
          wsConnected = true;
          updateWsStatus(true);
          
          // 清除重连定时器
          if (wsReconnectTimer) {
            clearTimeout(wsReconnectTimer);
            wsReconnectTimer = null;
          }
          
          // 发送心跳数据以保持连接
          startHeartbeat();
        };
        
        // 连接关闭事件
        socket.onclose = function() {
          console.log('WebSocket连接已关闭');
          wsConnected = false;
          updateWsStatus(false);
          
          // 自动重连
          if (!wsReconnectTimer) {
            wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
          }
        };
        
        // 连接错误事件
        socket.onerror = function(error) {
          console.error('WebSocket连接错误:', error);
          wsConnected = false;
          updateWsStatus(false);
          
          // 自动重连
          if (!wsReconnectTimer) {
            wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
          }
        };
        
        // 接收消息事件已被删除
        socket.onmessage = function(event) {
          // 不处理接收到的消息
        };
      } catch (error) {
        console.error('初始化WebSocket时出错:', error);
        wsConnected = false;
        updateWsStatus(false);
        
        // 自动重连
        if (!wsReconnectTimer) {
          wsReconnectTimer = setTimeout(initWebSocket, WS_RECONNECT_INTERVAL);
        }
      }
    }
    
    // 发送心跳数据以保持连接
    function startHeartbeat() {
      setInterval(() => {
        if (socket && socket.readyState === WebSocket.OPEN) {
          // 直接发送心跳数据，不再加密
          const heartbeatData = { type: 'heartbeat', timestamp: Date.now() };
          const heartbeatStr = JSON.stringify(heartbeatData);
          
          socket.send(heartbeatStr);
        }
      }, 25000); // 每25秒发送一次心跳
    }
    
    // 更新WebSocket状态UI
    function updateWsStatus(isConnected) {
      const wsStatusDot = document.getElementById('ws-status-dot');
      const wsStatusText = document.getElementById('ws-connection-status');
      
      if (wsStatusDot) {
        wsStatusDot.className = isConnected ? 'status-dot ws-online' : 'status-dot ws-offline';
      }
      
      if (wsStatusText) {
        wsStatusText.textContent = isConnected ? 'WS已连接' : 'WS未连接';
      }
    }
    
    // 在页面卸载前关闭WebSocket连接
    function closeWebSocketConnection() {
      if (socket) {
        socket.close();
        socket = null;
      }
    }
    
    document.addEventListener('DOMContentLoaded', function() {
      const apiUrlInput = document.getElementById('apiUrl');
      const customUidInput = document.getElementById('customUid');
      const toggleAutoRefreshBtn = document.getElementById('toggleAutoRefresh');
      const tableContainer = document.getElementById('tableContainer');
      const error = document.getElementById('error');
      const statusDot = document.getElementById('status-dot');
      const connectionStatus = document.getElementById('connection-status');
      const lastUpdate = document.getElementById('last-update');
      const nextUpdate = document.getElementById('next-update');
      const refreshIntervalInput = document.getElementById('refreshInterval');
      const applyIntervalBtn = document.getElementById('applyInterval');

      
      // 辅助函数：从多个可能的标签名中获取内容
      function getElementContent(element, tagNames, fallbackTags = []) {
        // 直接查找子元素
        for (const tagName of tagNames) {
          const el = element.querySelector(`:scope > ${tagName}`);
          if (el && el.textContent) {
            return el.textContent.trim();
          }
        }
        
        // 查找任意层级的子元素
        for (const tagName of tagNames) {
          const el = element.querySelector(tagName);
          if (el && el.textContent) {
            return el.textContent.trim();
          }
        }
        
        // 查找属性
        for (const tagName of tagNames) {
          if (element.getAttribute && element.getAttribute(tagName)) {
            return element.getAttribute(tagName).trim();
          }
        }
        
        // 使用备用标签
        for (const tagName of fallbackTags) {
          const el = element.querySelector(tagName);
          if (el && el.textContent) {
            return el.textContent.trim();
          }
          
          if (element.getAttribute && element.getAttribute(tagName)) {
            return element.getAttribute(tagName).trim();
          }
        }
        
        return '未知';
      }
      

      
      // 初始化WebSocket连接
      initWebSocket();
      
      // 在页面卸载前关闭WebSocket连接
      window.addEventListener('beforeunload', closeWebSocketConnection);
      

      
      // 删除常量DEFAULT_REFRESH_INTERVAL = 2.5，改为直接从输入框获取
      // 实际执行时会强制最小间隔为5秒，避免过于频繁的请求
      

      

      
      let refreshTimer;
      let nextRefreshTime;
      let autoRefreshEnabled = true;
      
      // 移除loadSavedData调用和WebSocket初始化
      setupRefreshTimer();
      
      fetchData();
      setInterval(updateStatusClock, 1000);
      
      // 移除以下输入框保存事件监听器
      // apiUrlInput.addEventListener('blur', saveData);
      // customUidInput.addEventListener('blur', saveData);
      
      toggleAutoRefreshBtn.addEventListener('click', function() {
        autoRefreshEnabled = !autoRefreshEnabled;
        this.textContent = autoRefreshEnabled ? "停止自动刷新" : "启用自动刷新";
        
        if (autoRefreshEnabled) {
          setupRefreshTimer();
        } else {
          if (refreshTimer) {
            clearInterval(refreshTimer);
            refreshTimer = null;
            nextUpdate.textContent = "自动刷新已关闭";
          }
        }
      });
      
      applyIntervalBtn.addEventListener('click', function() {
        const newInterval = parseInt(refreshIntervalInput.value);
        if (newInterval && newInterval >= 1000) {
          const configObj = { refreshInterval: newInterval / 1000 };
          localStorage.setItem('config', JSON.stringify(configObj));
          
          if (autoRefreshEnabled) {
            setupRefreshTimer(newInterval);
          }
          
          showMessage(`刷新间隔已更新为 ${newInterval} 毫秒`);
        } else {
          showMessage('请输入有效的刷新间隔，至少1000毫秒', true);
        }
      });
      
      function showMessage(message, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = isError ? 'error-message' : 'success-message';
        messageDiv.textContent = message;
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '10px';
        messageDiv.style.left = '50%';
        messageDiv.style.transform = 'translateX(-50%)';
        messageDiv.style.padding = '10px 15px';
        messageDiv.style.borderRadius = '4px';
        messageDiv.style.zIndex = '1000';
        
        if (isError) {
          messageDiv.style.backgroundColor = '#f8d7da';
          messageDiv.style.border = '1px solid #f5c6cb';
          messageDiv.style.color = '#721c24';
        } else {
          messageDiv.style.backgroundColor = '#d4edda';
          messageDiv.style.border = '1px solid #c3e6cb';
          messageDiv.style.color = '#155724';
        }
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
          document.body.removeChild(messageDiv);
        }, 3000);
      }
      
      function setupRefreshTimer(interval = null) {
        if (refreshTimer) {
          clearInterval(refreshTimer);
          refreshTimer = null;
        }
        
        if (interval === null) {
          interval = parseInt(refreshIntervalInput.value) || 2500;
        } else {
          const configObj = { refreshInterval: interval / 1000 };
          localStorage.setItem('config', JSON.stringify(configObj));
        }
        
        // 移除强制最小间隔
        // interval = Math.max(interval, 5000);
        
        if (interval > 0) {
          refreshTimer = setInterval(() => {
            refreshData();
          }, interval);
        }
      }
      
      function refreshData() {
        if (autoRefreshEnabled) {
          fetchData();
        }
      }
      
      function updateStatusClock() {
        if (nextRefreshTime && autoRefreshEnabled) {
          const now = new Date();
          const timeRemaining = Math.max(0, Math.floor((nextRefreshTime - now) / 1000));
          
          const minutes = Math.floor(timeRemaining / 60);
          const seconds = timeRemaining % 60;
          const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
          
          nextUpdate.textContent = `下次更新: ${formattedTime}`;
        }
      }
      
      /**
       * POST请求类型1: 获取足球比赛列表数据
       * 
       * 此函数发送POST请求获取所有进行中的足球比赛基础数据
       * 请求参数: p=get_game_list (获取比赛列表)
       * 
       * XML响应格式:
       * &lt;ec id="比赛ID"&gt;                  - 比赛容器节点，一个ec代表一场比赛
       *   &lt;game&gt;                         - 盘口信息节点，一场比赛可能有多个盘口
       *     &lt;TEAM_H&gt;主队名称&lt;/TEAM_H&gt;     - 主队名称
       *     &lt;TEAM_C&gt;客队名称&lt;/TEAM_C&gt;     - 客队名称
       *     &lt;SCORE_H&gt;主队得分&lt;/SCORE_H&gt;   - 主队得分
       *     &lt;SCORE_C&gt;客队得分&lt;/SCORE_C&gt;   - 客队得分
       *     &lt;LEAGUE&gt;联赛名称&lt;/LEAGUE&gt;     - 联赛名称
       *     &lt;RETIMESET&gt;比赛时间&lt;/RETIMESET&gt; - 比赛进行时间
       *     // 其他盘口信息
       *   &lt;/game&gt;
       * &lt;/ec&gt;
       * 
       * 转换为JSON的过程:
       * 1. 解析XML文档
       * 2. 提取所有&lt;ec&gt;节点
       * 3. 对每个节点调用processGameNode处理成JSON对象
       * 4. 将结果保存到全局变量window.ECID并显示到表格中
       */
      async function fetchData() {
        if (!autoRefreshEnabled) return;
        
        try {
          setConnectionStatus(true);
          
          // 定义超时ID变量
          let timeoutId;
          
          // 获取API请求地址和UID
          const apiUrl = apiUrlInput.value.trim() || 'https://m6686.com/transform.php';
          
          // 获取自定义UID
          const customUid = customUidInput.value.trim();
          
          // 从API URL提取ver参数
          let ver = '';
          try {
            const urlObj = new URL(apiUrl);
            ver = urlObj.searchParams.get('ver') || '';
          } catch (e) {
            console.warn('无法从URL提取ver参数，使用默认值');
            ver = '';
          }
          
          // 构建POST数据
          let postData = '';
          
          // 使用输入框当前值，不从localStorage加载
          postData = `uid=${customUid || 'ne4ko3ewqm37418875l5535514b1'}&ver=${ver}&langx=zh-cn&p=get_game_list&p3type=&date=&gtype=ft&showtype=live&rtype=rrnou&ltype=3&filter=&cupFantasy=N&sorttype=L&specialClick=&isFantasy=N`;
          //          uid=w86g966am37976245l6537270b1                   &ver=2025  &langx=zh-cn&p=get_game_list&p3type=&date=&gtype=ft&showtype=live&rtype=rrnou&ltype=3&filter=&cupFantasy=N&sorttype=L&specialClick=&isFantasy=N&ts=1752756804956
          // POST参数含义:
          // uid: 用户标识，用于API认证
          // ver: API版本号
          // langx: 语言设置，中文
          // p: 请求类型，获取比赛列表
          // p3type: 特殊比赛类型筛选器，空表示全部
          // date: 日期筛选，空表示当前日期
          // gtype=ft: 体育类型，ft表示足球(football)
          // showtype=live: 只显示直播中的比赛
          // rtype=rb: 滚球盘口类型，rb表示滚球盘(Running Ball)
          // ltype=3: 联赛类型筛选等级
          // filter: 附加筛选条件，空表示无特殊筛选
          // cupFantasy=N: 是否包括杯赛幻想赛事
          // sorttype=L: 排序方式，L表示按联赛分组
          // specialClick: 特殊点击事件标识
          // isFantasy=N: 是否包括幻想赛事
          // ts: 时间戳，保证每次请求唯一
          
          // 添加时间戳到请求中，确保每次请求都是唯一的
          const timestamp = Date.now();
          if (!postData.includes('ts=')) {
            postData = postData + '&ts=' + timestamp;
          } else {
            postData = postData.replace(/ts=\d+/, 'ts=' + timestamp);
          }
          
          // 将请求参数输出到控制台
          console.log('===== POST请求参数 =====');
          console.log('API URL:', apiUrl);
          console.log('POST数据:', postData);
          console.log('=======================');
          
          // 创建带超时的请求
          const controller = new AbortController();
          timeoutId = setTimeout(() => {
            controller.abort();
            console.warn('请求超时，已中止');
          }, 20000); // 超时时间为20秒
          
          // 发送POST请求获取数据
          const response = await fetch('/custom-game-data', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache'
            },
            body: JSON.stringify({ postData, apiUrl }),
            signal: controller.signal
          });
          
          // 请求成功，清除超时
          clearTimeout(timeoutId);
          
          // 输出响应状态信息
          console.log('===== 请求响应状态 =====');
          console.log('状态码:', response.status);
          console.log('状态:', response.ok ? '成功' : '失败');
          console.log('========================');
          
          if (!response.ok) {
            let errorText = '';
            try {
              const errorData = await response.json();
              errorText = errorData.message || errorData.error || '';
            } catch (e) {}
            
            throw new Error(`HTTP错误: ${response.status} ${errorText}`);
          }
          
          // 获取原始XML文本
          const xmlText = await response.text();
          
          // 将XML数据传入XML输入框
          const xmlInput = document.getElementById('xmlInput');
          if (xmlInput) {
            try {
              const formattedXml = formatXml(xmlText);
              xmlInput.value = formattedXml;
            } catch (error) {
              xmlInput.value = xmlText; // 如果格式化失败，使用原始XML
            }
            
            // 立即执行自动转换
            setTimeout(() => {
              autoConvertXmlToJson();
            }, 50);
          }
          
          // 调用convertXmlToCustomJson将XML转换为JSON格式
          const hgData = convertXmlToCustomJson(xmlText);
          

          
          // 将JSON数据发送到WebSocket服务器
          if (socket && socket.readyState === WebSocket.OPEN) {
            try {
              console.log('正在发送数据到WebSocket服务器...');
              
              // 直接发送JSON数据，不再加密
              const jsonStr = JSON.stringify(hgData);
              socket.send(jsonStr);
              console.log('数据已发送到WebSocket服务器');
            } catch (error) {
              console.error('发送数据到WebSocket服务器失败:', error);
            }
          } else {
            console.warn('WebSocket未连接，无法发送数据');
          }
          
          // 使用转换后的JSON数据显示表格
          displayDataTable(hgData);
          
          // 更新最后一次刷新时间
          const now = new Date();
          lastUpdate.textContent = `上次更新: ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
          
          // 如果启用了自动刷新，重置下次刷新时间
          if (autoRefreshEnabled) {
            // 从localStorage获取保存的刷新间隔或使用输入框默认值
            let savedConfig;
            try {
              const configStr = localStorage.getItem('config');
              if (configStr) {
                savedConfig = JSON.parse(configStr);
              }
            } catch (e) {
              console.warn('读取配置出错:', e);
            }
            
            // 从输入框获取默认刷新间隔值(毫秒)
            const defaultInterval = parseInt(refreshIntervalInput.value) || 2500;
            
            // 使用保存的配置或默认值计算实际刷新间隔(毫秒)，但不强制最小值
            const refreshInterval = (savedConfig?.refreshInterval * 1000) || defaultInterval;
            nextRefreshTime = new Date();
            nextRefreshTime.setTime(nextRefreshTime.getTime() + refreshInterval);
          }
          
          // 隐藏错误信息（如果有）
          hideError();
        } catch (err) {
          // 确保清除超时定时器
          if (timeoutId) {
            clearTimeout(timeoutId);
          }
          
          // 特殊处理 AbortError
          if (err.name === 'AbortError') {
            console.warn('请求被中止 (可能是超时)');
            showError('获取数据超时，将在下次刷新周期重试');
          } else {
            showError(err.message || '获取数据时发生错误');
            console.error('获取数据错误:', err);
          }
          
          setConnectionStatus(false);
        }
      }
      
      function displayDataTable(hgData) {
        // 保存当前表格中的比分数据，用于检测变化
        const currentScores = {};
        const oldRows = tableContainer.querySelectorAll('tr[data-game-id]');
        oldRows.forEach(row => {
          const gameId = row.getAttribute('data-game-id');
          // 修改选择器，考虑列的添加，比分现在是第4列
          const scoreCell = row.querySelector('td:nth-child(4)');
          if (scoreCell) {
            currentScores[gameId] = scoreCell.textContent;
          }
        });
        

        
        // 直接清空容器
        tableContainer.innerHTML = '';
        
        // 创建新的状态信息
        const dataStatusDiv = document.createElement('div');
        dataStatusDiv.className = 'data-status';
        dataStatusDiv.style.padding = '10px';
        dataStatusDiv.style.margin = '10px 0';
        dataStatusDiv.style.borderRadius = '5px';
        
        // 使用hgData.matches变量作为数据源
        const ecidData = hgData?.matches || [];
        
        // 使用所有比赛数据
        let filteredMatches = ecidData;
        
        if (filteredMatches.length === 0) {
          dataStatusDiv.style.backgroundColor = '#f8d7da';
          dataStatusDiv.style.border = '1px solid #f5c6cb';
          dataStatusDiv.innerHTML = '<strong>无比赛数据</strong> - 当前没有进行中的比赛';
          tableContainer.appendChild(dataStatusDiv);
          
          const noDataDiv = document.createElement('div');
          noDataDiv.className = 'no-data';
          noDataDiv.textContent = '没有找到比赛数据';
          tableContainer.appendChild(noDataDiv);
          
          return;
        } else {
          dataStatusDiv.style.backgroundColor = '#d4edda';
          dataStatusDiv.style.border = '1px solid #c3e6cb';
          dataStatusDiv.innerHTML = `<strong>✅ 成功获取 ${filteredMatches.length} 场比赛数据</strong>`;
          tableContainer.appendChild(dataStatusDiv);
        }
        
        // 创建表格
        const table = document.createElement('table');
        table.className = 'data-table';
        
        // 创建表头
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // 添加所需的表头列
        ['联赛', '滚球时间', '主队', '比分', '客队', '比赛时间', '全场让球(主/客)', '全场大小(大/小)', '上半场让球(主/客)', '上半场大小(大/小)'].forEach(header => {
          const th = document.createElement('th');
          th.textContent = header;
          headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // 创建表格内容
        const tbody = document.createElement('tbody');
        table.appendChild(tbody);
        
        // 保存检测到的比分变化
        const scoreChanges = [];
        
        // 遍历每个比赛
        filteredMatches.forEach((match, index) => {
          const row = document.createElement('tr');
          
          // 添加一个不可见的比赛ID属性，用于数据对比
          const gameId = match.id || index.toString();
          row.setAttribute('data-game-id', gameId);
          
          // 创建表格单元格并直接使用JSON数据
          
          // 1. 联赛
          const leagueCell = document.createElement('td');
          leagueCell.textContent = match.league || '';
          row.appendChild(leagueCell);
          
          // 2. 滚球时间
          const timeCell = document.createElement('td');
          const status = match.status || '';
          timeCell.textContent = status;
          row.appendChild(timeCell);
          
          // 3. 主队
          const homeCell = document.createElement('td');
          homeCell.textContent = match.teams?.home || '';
          row.appendChild(homeCell);
          
          // 4. 比分
          const scoreCell = document.createElement('td');
          const score = match.score || '';
          scoreCell.textContent = score;
          
          row.appendChild(scoreCell);
          
          // 检测比分变化
          if (currentScores[gameId] && currentScores[gameId] !== match.score) {
            scoreCell.classList.add('score-changed');
            
            // 记录变化信息
            scoreChanges.push({
              homeTeam: match.teams?.home || '',
              awayTeam: match.teams?.away || '',
              oldScore: currentScores[gameId],
              newScore: match.score || '',
              leagueName: match.league || '',
              matchTime: match.status || '',
              ecId: match.id || ''
            });
          }
          
          // 5. 客队
          const awayCell = document.createElement('td');
          awayCell.textContent = match.teams?.away || '';
          row.appendChild(awayCell);
          
          // 6. 比赛时间
          const matchTimeCell = document.createElement('td');
          matchTimeCell.textContent = match.dateTime || '';
          row.appendChild(matchTimeCell);
          
          // 7. 全场让球
          const fullHandicapCell = document.createElement('td');
          if (match.fullTimeHandicap && Array.isArray(match.fullTimeHandicap)) {
            fullHandicapCell.innerHTML = formatHandicapDisplay(match.fullTimeHandicap);
          }
          row.appendChild(fullHandicapCell);
          
          // 8. 全场大小
          const fullOverUnderCell = document.createElement('td');
          if (match.fullTimeOverUnder && Array.isArray(match.fullTimeOverUnder)) {
            fullOverUnderCell.innerHTML = formatOverUnderDisplay(match.fullTimeOverUnder);
          }
          row.appendChild(fullOverUnderCell);
          
          // 9. 上半场让球
          const halfHandicapCell = document.createElement('td');
          if (match.halfTimeHandicap && Array.isArray(match.halfTimeHandicap)) {
            halfHandicapCell.innerHTML = formatHandicapDisplay(match.halfTimeHandicap);
          }
          row.appendChild(halfHandicapCell);
          
          // 10. 上半场大小
          const halfOverUnderCell = document.createElement('td');
          if (match.halfTimeOverUnder && Array.isArray(match.halfTimeOverUnder)) {
            halfOverUnderCell.innerHTML = formatOverUnderDisplay(match.halfTimeOverUnder);
          }
          row.appendChild(halfOverUnderCell);
          
          tbody.appendChild(row);
        });
        
        tableContainer.appendChild(table);

        // 更新JSON树形显示
        if (jsonTreeViewer && hgData) {
          jsonTreeViewer.render(hgData);
        }

      }
      

      

      
      function showError(message) {
        error.textContent = message;
        error.style.display = 'block';
      }
      
      function hideError() {
        error.textContent = '';
        error.style.display = 'none';
      }
      
      function setConnectionStatus(isConnected) {
        const statusDot = document.querySelector('.status-dot');
        
        if (isConnected) {
          statusDot.className = 'status-dot online';
        } else {
          statusDot.className = 'status-dot offline';
        }
      }
      


      



      
      // 修改加载设置函数，移除apiUrl、customUid的部分
      function loadSettings() {
        // 移除从localStorage加载apiUrl、customUid部分
        /*
        const savedApiUrl = localStorage.getItem('apiUrl');
        const savedCustomUid = localStorage.getItem('customUid');
        */
        const savedRefreshInterval = localStorage.getItem('refreshInterval');
        const savedAutoRefresh = localStorage.getItem('autoRefresh');

        
        // 移除设置API地址、UID部分
        /*
        // 设置API地址
        if (savedApiUrl) {
          apiUrlInput.value = savedApiUrl;
        }

        // 设置UID
        if (savedCustomUid) {
          customUidInput.value = savedCustomUid;
        }
        */
        
        // 设置刷新间隔(如果有)
        if (savedRefreshInterval !== null) {
          refreshIntervalInput.value = savedRefreshInterval;
          
          // 如果启用了自动刷新，使用保存的间隔设置定时器
          if (autoRefreshEnabled) {
            setupRefreshTimer(parseInt(savedRefreshInterval));
          }
        }
        
        // 设置自动刷新状态
        if (savedAutoRefresh !== null) {
          autoRefreshEnabled = savedAutoRefresh === 'true';
          updateAutoRefreshButton();
        }
        

      }
      


      
      // 删除所有WebSocket相关函数
      

      
      // 加载保存的配置
      function loadSavedConfig() {
        const savedConfig = localStorage.getItem('config');
        if (savedConfig) {
          try {
            // 尝试解析保存的配置
            const config = JSON.parse(savedConfig);
            
            // 从配置中读取刷新间隔并更新到UI
            if (config && typeof config.refreshInterval === 'number') {
              // 将秒转换为毫秒显示在输入框中
              refreshIntervalInput.value = config.refreshInterval * 1000;
            }
          } catch (e) {
            console.error('解析保存的配置出错:', e);
          }
        }
      }
      
      // 该函数已移至全局作用域
      
      // HTML转义函数，防止XSS
      function escapeHtml(str) {
        return str
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#039;');
      }
      
      // 转换XML数据为JSON
      function convertXmlToJson(xmlData) {
        // 创建DOMParser对象解析XML字符串
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlData, "text/xml");
        
        // 提取所有ec节点（每个ec代表一场比赛）
        const ecNodes = xmlDoc.getElementsByTagName('ec');
        let matchesJson = [];
        
        // 如果找到ec节点，处理每个ec节点
        if (ecNodes.length > 0) {
          console.log(`找到${ecNodes.length}个ec节点`);
          
          Array.from(ecNodes).forEach(ec => {
            const ecId = ec.getAttribute('id') || '';
            const games = ec.getElementsByTagName('game');
            
            Array.from(games).forEach(game => {
              const processedGame = processGameNode(game);
              if (processedGame) matchesJson.push(processedGame);
            });
          });
        } else {
          // 如果没有找到ec节点，尝试直接查找game节点
          console.log("未找到ec节点，尝试查找game节点");
          const gameNodes = xmlDoc.getElementsByTagName('game');
          
          if (gameNodes.length > 0) {
            console.log(`找到${gameNodes.length}个game节点`);
            
            Array.from(gameNodes).forEach(game => {
              // 使用全局定义的processGameNode函数
              const processedGame = processGameNode(game);
              if (processedGame) matchesJson.push(processedGame);
            });
          } else {
            console.log("未找到任何比赛节点");
          }
        }
        
        return matchesJson;
      }
      
      // 处理单个game节点
      function processGameNode(game) {
        // 提取比赛基本信息和盘口数据
        // STRONG: 让球方向，'H'表示主队让球，'C'表示客队让球
        // TEAM_H/TEAM_C: 主队/客队名称
        // SCORE_H/SCORE_C: 主队/客队当前比分
        // LEAGUE: 联赛名称
        // DATETIME: 比赛时间(美东时间)
        // RETIMESET: 比赛进行时间，格式如"1H^15:00"表示上半场15分钟
        
        // 盘口相关字段:
        // RATIO_RE: 全场让球盘口值
        // RATIO_HRE: 半场让球盘口值
        // IOR_REH/IOR_REC: 全场主队/客队让球赔率
        // IOR_HREH/IOR_HREC: 半场主队/客队让球赔率
        // RATIO_ROUO/RATIO_ROUU: 全场大小球盘口值
        // RATIO_HROUO/RATIO_HROUU: 半场大小球盘口值
        // IOR_ROUH/IOR_ROUC: 全场小/大赔率
        // IOR_HROUH/IOR_HROUC: 半场小/大赔率
        
        // 获取让球方向
        const strong = getElementContent(game, ['STRONG'], []);
        
        // 获取EC ID - 先查找父元素
        let ecId = '';
        let parentEC = game.parentNode;
        if (parentEC && parentEC.tagName && parentEC.tagName.toLowerCase() === 'ec') {
          ecId = parentEC.getAttribute('id') || '';
        }
        
        // 如果父元素没有找到EC ID，则尝试通过document查询
        if (!ecId) {
          const ecElements = document.querySelectorAll('ec');
          for (const ec of ecElements) {
            if (ec.contains(game)) {
              ecId = ec.getAttribute('id') || '';
              break;
            }
          }
        }
        
        // 获取GID和LID
        const gid = getElementContent(game, ['GID'], []);
        const lid = getElementContent(game, ['LID'], []);
        
        // 获取比赛时间
        const matchTimeStr = getElementContent(game, ['DATETIME'], []);
        // 转换美东时间为北京时间 (美东时间+12小时)
        let beijingTime = '';
        if (matchTimeStr && matchTimeStr !== '未知') {
          try {
            // 提取MM-DD HH:MMa/p格式
            const matches = matchTimeStr.match(/(\d{2})-(\d{2})\s+(\d{1,2}):(\d{2})([ap])/i);
            if (matches) {
              const month = matches[1];
              const day = matches[2];
              let hours = parseInt(matches[3]);
              const minutes = matches[4];
              const ampm = matches[5].toLowerCase();
              
              // 处理12小时制
              if (ampm === 'p' && hours < 12) {
                hours += 12;
              } else if (ampm === 'a' && hours === 12) {
                hours = 0;
              }
              
              // 增加12小时得到北京时间
              let beijingHours = hours + 12;
              // 如果超过24小时，调整日期
              let beijingDay = parseInt(day);
              if (beijingHours >= 24) {
                beijingHours -= 24;
                beijingDay += 1;
              }
              
              // 格式化北京时间
              beijingTime = `${month}-${beijingDay.toString().padStart(2, '0')} ${beijingHours.toString().padStart(2, '0')}:${minutes}`;
            }
          } catch (e) {
            console.error('转换比赛时间出错:', e);
            beijingTime = matchTimeStr;
          }
        }
        
        // 获取比赛进行时间
        const gameTimeStr = getElementContent(game, ['RETIMESET'], []);
        let gameTime = '';
        let gamePeriod = '';
        
        // 处理比赛时间格式
        if (gameTimeStr && gameTimeStr !== '未知') {
          if (gameTimeStr.includes('1H^')) {
            gameTime = gameTimeStr.replace('1H^', '');
            gamePeriod = '上半场';
          } else if (gameTimeStr.includes('2H^')) {
            gameTime = gameTimeStr.replace('2H^', '');
            gamePeriod = '下半场';
          } else {
            gameTime = gameTimeStr;
          }
        }
        
        // 处理全场让球盘口和赔率 - 不做条件检查，直接使用
        const handicapValue = getElementContent(game, ['RATIO_RE'], []);
        let homeHandicap = handicapValue;
        let awayHandicap = handicapValue;
        const homeOdds = getElementContent(game, ['IOR_REH'], []);
        const awayOdds = getElementContent(game, ['IOR_REC'], []);
        
        // 根据强队情况添加正负号
        if (handicapValue && handicapValue !== '0' && handicapValue !== '未知') {
          // 标准化盘口值格式 - 删除多余空格
          homeHandicap = homeHandicap.replace(/\s+/g, '');
          awayHandicap = awayHandicap.replace(/\s+/g, '');
          
          if (strong === 'H') {  // 主队是强队
            homeHandicap = homeHandicap.startsWith('-') ? homeHandicap : '-' + homeHandicap;
            awayHandicap = awayHandicap.startsWith('+') ? awayHandicap : '+' + awayHandicap;
          } else if (strong === 'C') {  // 客队是强队
            homeHandicap = homeHandicap.startsWith('+') ? homeHandicap : '+' + homeHandicap;
            awayHandicap = awayHandicap.startsWith('-') ? awayHandicap : '-' + awayHandicap;
          }
        }
        
        // 处理上半场让球盘口和赔率 - 不做条件检查，直接使用
        const halfHandicapValue = getElementContent(game, ['RATIO_HRE'], []);
        let homeHalfHandicap = halfHandicapValue;
        let awayHalfHandicap = halfHandicapValue;
        const halfHomeOdds = getElementContent(game, ['IOR_HREH'], []);
        const halfAwayOdds = getElementContent(game, ['IOR_HREC'], []);
        
        // 根据强队情况添加正负号
        if (halfHandicapValue && halfHandicapValue !== '0' && halfHandicapValue !== '未知') {
          // 标准化盘口值格式 - 删除多余空格
          homeHalfHandicap = homeHalfHandicap.replace(/\s+/g, '');
          awayHalfHandicap = awayHalfHandicap.replace(/\s+/g, '');
          
          if (strong === 'H') {  // 主队是强队
            homeHalfHandicap = homeHalfHandicap.startsWith('-') ? homeHalfHandicap : '-' + homeHalfHandicap;
            awayHalfHandicap = awayHalfHandicap.startsWith('+') ? awayHalfHandicap : '+' + awayHalfHandicap;
          } else if (strong === 'C') {  // 客队是强队
            homeHalfHandicap = homeHalfHandicap.startsWith('+') ? homeHalfHandicap : '+' + homeHalfHandicap;
            awayHalfHandicap = awayHalfHandicap.startsWith('-') ? awayHalfHandicap : '-' + awayHalfHandicap;
          }
        }
        
        // 获取全场大小盘口和赔率 - 不做条件检查，直接使用
        const totalOverValue = getElementContent(game, ['RATIO_ROUO'], []);
        const totalUnderValue = getElementContent(game, ['RATIO_ROUU'], []);
        const totalOverOdds = getElementContent(game, ['IOR_ROUC'], []);
        const totalUnderOdds = getElementContent(game, ['IOR_ROUH'], []);
        
        // 获取上半场大小盘口和赔率 - 不做条件检查，直接使用
        const halfTotalOverValue = getElementContent(game, ['RATIO_HROUO'], []);
        const halfTotalUnderValue = getElementContent(game, ['RATIO_HROUU'], []);
        const halfOverOdds = getElementContent(game, ['IOR_HROUC'], []);
        const halfUnderOdds = getElementContent(game, ['IOR_HROUH'], []);
        
        // 提取基本信息
        const gameObj = {
          ecId: ecId || '',  // 添加EC ID
          gid: gid || '',    // 添加GID
          lid: lid || '',    // 添加LID
          id: game.getAttribute('id') || '',
          league: getElementContent(game, ['LEAGUE'], []),
          homeTeam: getElementContent(game, ['TEAM_H'], []),
          awayTeam: getElementContent(game, ['TEAM_C'], []),
          score: `${getElementContent(game, ['SCORE_H'], []) || '0'}-${getElementContent(game, ['SCORE_C'], []) || '0'}`,
          matchTime: beijingTime || '',  // 添加北京时间
          gameTime: {
            period: gamePeriod || '',  // 添加比赛阶段
            time: gameTime || ''      // 添加比赛进行时间
          },
          redCards: {
            home: getElementContent(game, ['REDCARD_H'], []) || '0',
            away: getElementContent(game, ['REDCARD_C'], []) || '0'
          },
          odds: {
            fullTime: {
              handicap: {
                home: {
                  handicap: homeHandicap || '',
                  odds: homeOdds || ''
                },
                away: {
                  handicap: awayHandicap || '',
                  odds: awayOdds || ''
                }
              },
              total: {
                over: {
                  handicap: totalOverValue ? `大 ${totalOverValue.replace(/\s+/g, '')}` : '',
                  odds: totalOverOdds || ''
                },
                under: {
                  handicap: totalUnderValue ? `小 ${totalUnderValue.replace(/\s+/g, '')}` : '',
                  odds: totalUnderOdds || ''
                }
              }
            },
            halfTime: {
              handicap: {
                home: {
                  handicap: homeHalfHandicap || '',
                  odds: halfHomeOdds || ''
                },
                away: {
                  handicap: awayHalfHandicap || '',
                  odds: halfAwayOdds || ''
                }
              },
              total: {
                over: {
                  handicap: halfTotalOverValue ? `大 ${halfTotalOverValue.replace(/\s+/g, '')}` : '',
                  odds: halfOverOdds || ''
                },
                under: {
                  handicap: halfTotalUnderValue ? `小 ${halfTotalUnderValue.replace(/\s+/g, '')}` : '',
                  odds: halfUnderOdds || ''
                }
              }
            }
          }
        };
        
        // 直接返回不做过滤
        return gameObj;
      }
      
      // 改进的removeEmptyValues函数
      function removeEmptyValues(obj) {
        if (obj === null || obj === undefined) {
          return obj;
        }
        
        // 如果是数组，过滤数组中的空值
        if (Array.isArray(obj)) {
          return obj.map(item => removeEmptyValues(item)).filter(item => 
            item !== null && 
            item !== undefined && 
            item !== '' && 
            item !== '未知' &&
            item !== '-未知' &&
            item !== '+未知' &&
            item !== '大 未知' &&
            item !== '小 未知');
        }
        
        // 如果是对象，递归处理对象的属性
        if (typeof obj === 'object') {
          const result = {};
          for (const key in obj) {
            // 如果属性是odds对象，保留所有数据不做过滤
            if (key === 'odds') {
              result[key] = obj[key];
              continue;
            }
            
            const value = removeEmptyValues(obj[key]);
            if (value !== null && 
                value !== undefined && 
                value !== '' && 
                value !== '未知' &&
                value !== '-未知' &&
                value !== '+未知' &&
                value !== '大 未知' &&
                value !== '小 未知') {
              result[key] = value;
            }
          }
          return Object.keys(result).length > 0 ? result : null;
        }
        
        // 处理字符串值中的"未知"
        if (typeof obj === 'string') {
          return obj === '未知' || 
                 obj === '-未知' || 
                 obj === '+未知' || 
                 obj === '大 未知' || 
                 obj === '小 未知' ? '' : obj;
        }
        
        return obj;
      }
    });


    
    // XML自动转换函数 - 定义为全局函数
    function autoConvertXmlToJson() {
      const xmlInput = document.getElementById('xmlInput');

      if (!xmlInput) return;

      try {
        const xmlText = xmlInput.value.trim();
        if (!xmlText) {
          // 清空树形显示
          if (jsonTreeViewer) {
            jsonTreeViewer.render(null);
          }
          return;
        }

        // 解析XML
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

        // 检查解析错误
        const parseError = xmlDoc.querySelector('parsererror');
        if (parseError) {
          // 显示错误信息
          if (jsonTreeViewer) {
            const container = document.getElementById('jsonTreeContainer');
            container.innerHTML = '<div style="color: #e74c3c; text-align: center; padding: 20px;">转换失败: XML格式错误</div>';
          }
          return;
        }

        // 转换为HG格式的JSON
        const jsonData = convertXmlToHgJson(xmlDoc);

        // 使用树形结构显示JSON
        if (jsonTreeViewer) {
          jsonTreeViewer.render(jsonData);
        }

      } catch (error) {
        console.error('XML转换失败:', error);
        // 显示错误信息
        if (jsonTreeViewer) {
          const container = document.getElementById('jsonTreeContainer');
          container.innerHTML = `<div style="color: #e74c3c; text-align: center; padding: 20px;">转换失败: ${error.message}</div>`;
        }
      }
    }

    // XML转JSON功能
    document.addEventListener('DOMContentLoaded', function() {
      const xmlInput = document.getElementById('xmlInput');

      if (xmlInput) {
        // XML输入框自动格式化和转换功能
        xmlInput.addEventListener('paste', function(e) {
          // 延迟执行格式化和转换，确保粘贴内容已经插入
          setTimeout(() => {
            try {
              const xmlText = xmlInput.value.trim();
              if (xmlText) {
                const formattedXml = formatXml(xmlText);
                xmlInput.value = formattedXml;
              }
              // 自动转换JSON
              autoConvertXmlToJson();
            } catch (error) {
              console.warn('XML格式化失败:', error);
              // 即使格式化失败也尝试转换
              autoConvertXmlToJson();
            }
          }, 10);
        });
        
        // 也支持手动输入后的格式化和转换（失去焦点时）
        xmlInput.addEventListener('blur', function() {
          try {
            const xmlText = xmlInput.value.trim();
            if (xmlText) {
              const formattedXml = formatXml(xmlText);
              xmlInput.value = formattedXml;
            }
          } catch (error) {
            console.warn('XML格式化失败:', error);
          }
          // 自动转换JSON
          autoConvertXmlToJson();
        });
        
        // 输入时实时转换
        xmlInput.addEventListener('input', function() {
          autoConvertXmlToJson();
        });
        
        // 页面加载时检查是否已有内容并自动转换
        if (xmlInput.value.trim()) {
          autoConvertXmlToJson();
        }
      }
    });
    
    // 将XML转换为HG格式的JSON (与WebSocket发送的格式完全一致)
    function convertXmlToHgJson(xmlDoc) {
      // 先将xmlDoc转换回字符串
      const serializer = new XMLSerializer();
      const xmlString = serializer.serializeToString(xmlDoc);
      
      // 调用convertXmlToCustomJson函数
      return convertXmlToCustomJson(xmlString);
    }

    // 格式化让球盘口显示
    function formatHandicapDisplay(handicapArray) {
      if (!Array.isArray(handicapArray) || handicapArray.length === 0) {
        return '';
      }
      
      // 显示主队盘口部分
      const homeHandicapLine = handicapArray.map(item => 
        `<span style="color: black;">${item.homeHandicap}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      const homeOddsLine = handicapArray.map(item => 
        `<span style="color: red;">${item.homeOdds}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      // 显示客队盘口部分
      const awayHandicapLine = handicapArray.map(item => 
        `<span style="color: black;">${item.awayHandicap}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      const awayOddsLine = handicapArray.map(item => 
        `<span style="color: red;">${item.awayOdds}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');
      
      return homeHandicapLine + '<br>' + homeOddsLine + '<br><br>' + awayHandicapLine + '<br>' + awayOddsLine;
    }
    
    // 格式化大小球盘口显示
    function formatOverUnderDisplay(overUnderArray) {
      if (!Array.isArray(overUnderArray) || overUnderArray.length === 0) {
        return '';
      }

      // 显示大球部分 - 使用 overTotal 属性
      const overLine = overUnderArray.map(item =>
        `<span style="color: black;">${item.overTotal || '大 --'}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');

      const overOddsLine = overUnderArray.map(item =>
        `<span style="color: red;">${item.overOdds || '--'}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');

      // 显示小球部分 - 使用 underTotal 属性
      const underLine = overUnderArray.map(item =>
        `<span style="color: black;">${item.underTotal || '小 --'}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');

      const underOddsLine = overUnderArray.map(item =>
        `<span style="color: red;">${item.underOdds || '--'}</span>`
      ).join('&nbsp;&nbsp;&nbsp;');

      return overLine + '<br>' + overOddsLine + '<br><br>' + underLine + '<br>' + underOddsLine;
    }
    
    // XML格式化函数
    function formatXml(xml) {
      try {
        // 移除多余的空白字符
        xml = xml.replace(/>\s*</g, '><');
        
        // 解析XML以验证格式
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xml, 'text/xml');
        
        // 检查解析错误
        const parseError = xmlDoc.querySelector('parsererror');
        if (parseError) {
          // 如果解析失败，返回原始文本
          return xml;
        }
        
        // 使用XMLSerializer重新序列化
        const serializer = new XMLSerializer();
        let formatted = serializer.serializeToString(xmlDoc);
        
        // 添加缩进格式化
        let indent = 0;
        const tab = '  '; // 两个空格作为缩进
        
        formatted = formatted.replace(/(>)(<)(\/*)/g, '$1\n$2$3');
        
        const lines = formatted.split('\n');
        const formattedLines = [];
        
        lines.forEach(line => {
          line = line.trim();
          if (line) {
            // 处理结束标签
            if (line.match(/^<\/\w/)) {
              indent--;
            }
            
            // 添加缩进
            formattedLines.push(tab.repeat(Math.max(0, indent)) + line);
            
            // 处理开始标签（不是自闭合标签且不是结束标签）
            if (line.match(/^<\w[^>]*[^\/]>$/)) {
              indent++;
            }
          }
        });
        
        return formattedLines.join('\n');
      } catch (error) {
        console.warn('XML格式化失败:', error);
        return xml; // 如果格式化失败，返回原始XML
      }
    }

    // JSON树形结构实现
    class JsonTreeViewer {
      constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.nodeCounter = 0;
        this.jsonData = null;
      }

      // 渲染JSON数据为树形结构
      render(jsonData) {
        this.jsonData = jsonData;
        this.nodeCounter = 0;

        if (!jsonData) {
          this.container.innerHTML = '<div style="color: #999; text-align: center; padding: 20px;">暂无数据</div>';
          return;
        }

        const treeHtml = this.renderNode(jsonData, '', 0, true);
        this.container.innerHTML = `<div class="json-tree">${treeHtml}</div>`;

        // 绑定事件
        this.bindEvents();
      }

      // 渲染单个节点
      renderNode(value, key, level, isLast = false) {
        const nodeId = `node_${this.nodeCounter++}`;
        const type = this.getValueType(value);
        const icon = this.getTypeIcon(type);
        const levelClass = level <= 15 ? `level-${level}` : 'deep-level';
        const levelStyle = level > 15 ? `style="--level: ${level}"` : '';
        const lastChildClass = isLast ? 'last-child' : '';

        let html = '';

        if (type === 'object' || type === 'array') {
          const keys = Object.keys(value);
          const hasChildren = keys.length > 0;
          const countText = type === 'object' ? `${keys.length} keys` : `${keys.length} items`;

          html += `<div class="json-node ${levelClass} ${lastChildClass}" ${levelStyle} style="--level: ${level}">`;

          if (hasChildren) {
            html += `<span class="json-toggle" data-node-id="${nodeId}">⊞</span>`;
          } else {
            html += `<span style="width: 14px; display: inline-block;"></span>`;
          }

          html += `<span class="json-icon ${type}">${icon}</span>`;

          if (key) {
            html += `<span class="json-key">"${key}"</span>: `;
          }

          html += `<span class="json-value">${type === 'object' ? '{' : '['}</span>`;

          if (hasChildren) {
            html += `<span class="json-count" data-node-id="${nodeId}" style="display: none;">${countText}</span>`;
            html += `</div>`;
            html += `<div class="json-children" data-node-id="${nodeId}">`;

            keys.forEach((childKey, index) => {
              const isLastChild = index === keys.length - 1;
              html += this.renderNode(value[childKey], childKey, level + 1, isLastChild);
            });

            html += `</div>`;
            html += `<div class="json-node ${levelClass}" ${levelStyle} style="--level: ${level}">`;
            html += `<span style="width: 14px; display: inline-block;"></span>`;
            html += `<span style="width: 20px; display: inline-block;"></span>`;
            html += `<span class="json-value">${type === 'object' ? '}' : ']'}</span>`;
          } else {
            html += `<span class="json-value">${type === 'object' ? '}' : ']'}</span>`;
          }

          html += `</div>`;
        } else {
          // 基本类型
          html += `<div class="json-node ${levelClass} ${lastChildClass}" ${levelStyle} style="--level: ${level}">`;
          html += `<span style="width: 14px; display: inline-block;"></span>`;
          html += `<span class="json-icon ${type}">${icon}</span>`;

          if (key) {
            html += `<span class="json-key">"${key}"</span>: `;
          }

          html += `<span class="json-value ${type}">${this.formatValue(value, type)}</span>`;
          html += `</div>`;
        }

        return html;
      }

      // 获取值的类型
      getValueType(value) {
        if (value === null) return 'null';
        if (Array.isArray(value)) return 'array';
        if (typeof value === 'object') return 'object';
        if (typeof value === 'string') return 'string';
        if (typeof value === 'number') return 'number';
        if (typeof value === 'boolean') return 'boolean';
        return 'string';
      }

      // 获取类型图标
      getTypeIcon(type) {
        const icons = {
          object: '象',
          array: '组',
          string: '文',
          number: '数',
          boolean: '布',
          null: '空'
        };
        return icons[type] || '?';
      }

      // 格式化值显示
      formatValue(value, type) {
        if (type === 'string') {
          return `"${value}"`;
        } else if (type === 'null') {
          return 'null';
        } else {
          return String(value);
        }
      }

      // 绑定事件
      bindEvents() {
        // 折叠/展开事件
        this.container.addEventListener('click', (e) => {
          if (e.target.classList.contains('json-toggle')) {
            const nodeId = e.target.getAttribute('data-node-id');
            this.toggleNode(nodeId);
          }
        });
      }

      // 切换节点展开/折叠状态
      toggleNode(nodeId) {
        const toggle = this.container.querySelector(`[data-node-id="${nodeId}"].json-toggle`);
        const children = this.container.querySelector(`[data-node-id="${nodeId}"].json-children`);
        const count = this.container.querySelector(`[data-node-id="${nodeId}"].json-count`);

        if (children && toggle) {
          const isCollapsed = children.classList.contains('collapsed');

          if (isCollapsed) {
            // 展开
            children.classList.remove('collapsed');
            toggle.textContent = '⊞';
            if (count) count.style.display = 'none';
          } else {
            // 折叠
            children.classList.add('collapsed');
            toggle.textContent = '⊟';
            if (count) count.style.display = 'inline';
          }
        }
      }

      // 展开全部
      expandAll() {
        const toggles = this.container.querySelectorAll('.json-toggle');
        toggles.forEach(toggle => {
          const nodeId = toggle.getAttribute('data-node-id');
          const children = this.container.querySelector(`[data-node-id="${nodeId}"].json-children`);
          const count = this.container.querySelector(`[data-node-id="${nodeId}"].json-count`);

          if (children) {
            children.classList.remove('collapsed');
            toggle.textContent = '⊞';
            if (count) count.style.display = 'none';
          }
        });
      }

      // 折叠全部
      collapseAll() {
        const toggles = this.container.querySelectorAll('.json-toggle');
        toggles.forEach(toggle => {
          const nodeId = toggle.getAttribute('data-node-id');
          const children = this.container.querySelector(`[data-node-id="${nodeId}"].json-children`);
          const count = this.container.querySelector(`[data-node-id="${nodeId}"].json-count`);

          if (children) {
            children.classList.add('collapsed');
            toggle.textContent = '⊟';
            if (count) count.style.display = 'inline';
          }
        });
      }

      // 复制JSON
      copyJson() {
        if (this.jsonData) {
          const jsonString = JSON.stringify(this.jsonData, null, 2);
          navigator.clipboard.writeText(jsonString).then(() => {
            alert('JSON已复制到剪贴板');
          }).catch(() => {
            // 降级方案
            const textarea = document.createElement('textarea');
            textarea.value = jsonString;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
            alert('JSON已复制到剪贴板');
          });
        }
      }

      // 导出JSON文件
      exportJson() {
        if (this.jsonData) {
          const jsonString = JSON.stringify(this.jsonData, null, 2);
          const blob = new Blob([jsonString], { type: 'application/json' });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = 'hg_data.json';
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      }
    }

    // 初始化JSON树形查看器
    let jsonTreeViewer;

    document.addEventListener('DOMContentLoaded', function() {
      // 等待DOM加载完成后初始化
      setTimeout(() => {
        jsonTreeViewer = new JsonTreeViewer('jsonTreeContainer');

        // 绑定按钮事件
        const expandAllBtn = document.getElementById('expandAllBtn');
        const collapseAllBtn = document.getElementById('collapseAllBtn');
        const copyJsonBtn = document.getElementById('copyJsonBtn');
        const exportJsonBtn = document.getElementById('exportJsonBtn');

        if (expandAllBtn) {
          expandAllBtn.addEventListener('click', () => jsonTreeViewer.expandAll());
        }

        if (collapseAllBtn) {
          collapseAllBtn.addEventListener('click', () => jsonTreeViewer.collapseAll());
        }

        if (copyJsonBtn) {
          copyJsonBtn.addEventListener('click', () => jsonTreeViewer.copyJson());
        }

        if (exportJsonBtn) {
          exportJsonBtn.addEventListener('click', () => jsonTreeViewer.exportJson());
        }
      }, 100);
    });

    // 标签页切换函数
    function switchTab(tabId) {
      // 隐藏所有标签页内容
      const tabContents = document.querySelectorAll('.tab-content');
      tabContents.forEach(content => {
        content.classList.remove('active');
      });

      // 移除所有标签按钮的active类
      const tabButtons = document.querySelectorAll('.tab-button');
      tabButtons.forEach(button => {
        button.classList.remove('active');
      });

      // 显示选中的标签页内容
      const selectedTab = document.getElementById(tabId);
      if (selectedTab) {
        selectedTab.classList.add('active');
      }

      // 激活对应的标签按钮
      const selectedButton = event.target;
      if (selectedButton) {
        selectedButton.classList.add('active');
      }
    }

  </script>
</body>
</html>
