// Content script - 在网页中运行的脚本
// 这个文件可以访问网页的DOM，但不能直接访问Chrome扩展API

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'getPageSource') {
    // 获取页面的完整HTML源码
    const pageSource = document.documentElement.outerHTML;
    sendResponse({ source: pageSource });
  }
  return true; // 保持消息通道开放
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
  console.log('网页源码获取器 - Content Script 已加载');
});
