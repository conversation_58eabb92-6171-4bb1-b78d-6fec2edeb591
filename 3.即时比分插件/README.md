# 网页源码获取器 Chrome 插件

这是一个简单的 Chrome 浏览器插件，可以获取当前网页的完整源码。

## 功能特点

- 一键获取当前网页的完整 HTML 源码
- 简洁的用户界面
- 支持所有网站

## 安装方法

1. 打开 Chrome 浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择这个插件的文件夹（即时比分插件）
6. 插件安装完成

## 使用方法

1. 安装插件后，浏览器工具栏会出现插件图标
2. 打开任意网页
3. 点击插件图标
4. 在弹出窗口中点击"获取当前网页源码"按钮
5. 网页源码将显示在文本框中

## 文件说明

- `manifest.json` - 插件配置文件
- `popup.html` - 插件弹出窗口的界面
- `popup.js` - 弹出窗口的逻辑代码
- `content.js` - 在网页中运行的内容脚本

## 注意事项

- 插件需要"activeTab"和"scripting"权限来访问当前标签页内容
- 某些特殊页面（如 chrome:// 页面）可能无法获取源码
